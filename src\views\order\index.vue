﻿<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-card">
        <div class="search-header">
          <i class="el-icon-search search-icon" />
          <span class="search-title">订单查询</span>
        </div>

        <div class="search-content">
          <div class="search-input-wrapper">
            <el-input
              v-model="phone"
              placeholder="手机号"
              prefix-icon="el-icon-mobile-phone"
              clearable
            />
          </div>

          <div class="search-input-wrapper">
            <el-input
              v-model="orderno"
              placeholder="订单号"
              prefix-icon="el-icon-document"
              clearable
            />
          </div>

          <div class="search-input-wrapper">
            <el-input
              v-model="name"
              placeholder="真实姓名"
              prefix-icon="el-icon-user"
              clearable
            />
          </div>

          <div class="search-input-wrapper">
            <el-input
              v-model="carPlate"
              placeholder="请输入车牌号"
              prefix-icon="el-icon-truck"
              clearable
              maxlength="8"
              show-word-limit
            />
          </div>

          <div class="search-button-wrapper">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchOrder()"
            >
              查询
            </el-button>
            <el-button icon="el-icon-refresh-left" @click="resetSearch()">
              重置
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和操作区域 -->
    <div class="filter-section">
      <div class="filter-card">
        <div class="filter-selector">
          <div class="selector-wrapper">
            <label class="selector-label">订单状态：</label>
            <el-select
              v-model="activeButton"
              placeholder="请选择订单状态"
              class="status-selector"
              @change="changeOrderState"
            >
              <el-option
                v-for="item in orderStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
        </div>

        <div class="date-picker-wrapper">
          <el-date-picker
            v-model="value1"
            :default-value="new Date()"
            :unlink-panels="true"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            align="right"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            class="date-picker"
            @change="change"
          />
        </div>

        <div class="action-buttons">
          <el-button
            type="success"
            icon="el-icon-download"
            @click="downLoadExcex()"
          >
            下载Excel表
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <i class="el-icon-s-order" />
            <span>订单列表</span>
          </div>
        </div>

        <el-table
          v-loading="loadingTable"
          :data="showData"
          stripe
          style="width: 100%"
          class="modern-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#374151',
            fontWeight: '600',
          }"
          :row-style="{ height: '60px' }"
        >
          <el-table-column
            prop="phone"
            label="用户ID"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span class="user-id">#{{ scope.row.phone }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="invitation"
            label="所属代理"
            align="center"
            min-width="120"
          >
            <template slot-scope="scope">
              <span class="agent-info">{{ scope.row.invitation || "-" }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="equity_type" label="商品名" min-width="150">
            <template slot-scope="scope">
              <div class="product-name-cell">
                <i class="el-icon-goods product-icon" />
                <span>{{ dictKey[scope.row.equity_type] }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="orderNo" label="订单号" min-width="180">
            <template slot-scope="scope">
              <span class="order-no">{{ scope.row.orderNo }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="order_money"
            label="价格"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span class="price-text">¥{{ scope.row.order_money }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="real_name" label="绑定姓名" min-width="120">
            <template slot-scope="scope">
              <span class="real-name">{{ scope.row.real_name || "-" }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="phnumber" label="绑定手机号" min-width="140">
            <template slot-scope="scope">
              <span class="phone-number">{{ scope.row.phnumber || "-" }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="address"
            label="绑定地址"
            min-width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span class="address">{{ scope.row.address || "-" }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="car_id" label="绑定车牌号" min-width="120">
            <template slot-scope="scope">
              <span class="car-id">{{ scope.row.car_id || "-" }}</span>
            </template>
          </el-table-column>

          <el-table-column label="下单状态" width="120" align="center">
            <template slot-scope="scope">
              <el-tag
                :type="getOrderStatusType(scope.row.order_status)"
                size="small"
                class="status-tag"
              >
                {{ getOrderStatus(scope.row.order_status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="总退款金额" width="120" align="center">
            <template slot-scope="scope">
              <span class="refund-amount"
                >¥{{ getRefund(scope.row.refund) }}</span
              >
            </template>
          </el-table-column>

          <el-table-column
            prop="order_status"
            label="订单状态"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag
                :type="
                  getRefundStatus(scope.row.refundStatus) === '无退款'
                    ? 'success'
                    : 'danger'
                "
                size="small"
                class="status-tag"
              >
                {{
                  getRefundStatus(scope.row.refundStatus) === "无退款"
                    ? "正常订单"
                    : getRefundStatus(scope.row.refundStatus)
                }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="order_time" label="下单时间" min-width="160">
            <template slot-scope="scope">
              <span class="order-time">{{ scope.row.order_time }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="pay_platform"
            label="支付平台"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span class="pay-platform">{{
                scope.row.pay_platform || "-"
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            width="120"
            align="center"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="danger"
                plain
                size="small"
                icon="el-icon-refresh-left"
                :disabled="
                  scope.row.order_status === 'FAILED' ||
                  scope.row.order_money === scope.row.refund
                "
                class="action-btn refund-btn"
                @click="clickRefund(scope.row)"
              >
                退款
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination
        background
        layout="total, prev, pager, next, jumper"
        :total="filterParam.total"
        :page-size="filterParam.limit"
        :current-page="filterParam.page"
        class="modern-pagination"
        @current-change="changePage"
      />
    </div>
  </div>
</template>

<script>
import * as xlsx from "xlsx";
import { saveAs } from "file-saver";
import {
  pageByPramas,
  allcommodity,
  searchOrderByPage,
  walletWithdraw,
  getQykExcel,
} from "@/api/order";
import { refund, queryRefundMoney } from "@/api/refund";
import { allOrders } from "@/api/excel";
export default {
  data() {
    return {
      showData: [],
      filterParam: {
        page: 1,
        limit: 10,
        total: 0,
        phone: "",
        orderno: "",
        name: "",
        carPlate: "",
      },
      phone: "",
      orderno: "",
      dictKey: {},
      refund_amt: 0,
      name: "",
      carPlate: "",
      loadingTable: true,
      searchorderstate: "",
      value1: "",
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      activeButton: 0,
      withdrawAmt: "",
      orderStatusOptions: [
        { label: "全部", value: 0 },
        { label: "已完成", value: 1 },
        { label: "已失败", value: 2 },
        { label: "已退款", value: 4 },
      ],
    };
  },
  mounted() {
    this.setDefaultTime();
    this.pageByPramas();
    this.allcommodity();
  },
  methods: {
    // 提现
    withdraw() {
      const data = {
        amt: this.withdrawAmt,
      };
      walletWithdraw(data).then((res) => {
        console.log(res);
        if (res.data.message === "success") {
          // 提现成功，不显示消息提示
        } else {
          this.$message.error("提现失败");
        }
      });
    },
    // 搜索
    searchOrder() {
      this.filterParam.phone = this.phone;
      this.filterParam.orderno = this.orderno;
      this.filterParam.name = this.name;
      this.filterParam.carPlate = this.carPlate;
      this.pageByPramas();
    },
    // 重置搜索条件
    resetSearch() {
      this.phone = "";
      this.orderno = "";
      this.name = "";
      this.carPlate = "";
      this.filterParam.phone = "";
      this.filterParam.orderno = "";
      this.filterParam.name = "";
      this.filterParam.carPlate = "";
      this.pageByPramas();
    },
    // 获取数据
    pageByPramas() {
      this.loadingTable = true;

      const data = {
        page: this.filterParam.page,
        limit: this.filterParam.limit,
      };
      if (this.value1) {
        data.start_time = this.formatDate(this.value1[0]);
        data.end_time = this.formatDate(this.value1[1]);
      }
      if (this.filterParam.phone !== "") {
        data.phone = this.filterParam.phone;
      }
      if (this.filterParam.orderno !== "") {
        data.orderno = this.filterParam.orderno;
      }
      if (this.filterParam.name !== "") {
        data.name = this.filterParam.name;
      }
      if (this.filterParam.carPlate !== "") {
        data.car_id = this.filterParam.carPlate;
      }
      if (this.searchorderstate !== "") {
        data.order_status = this.searchorderstate;
      }
      console.log(data);
      searchOrderByPage(data).then((res) => {
        console.log(res);
        res = res.data.data;
        this.filterParam.total = res.total;
        const data = res.list;
        this.showData = data;
        this.loadingTable = false;
      });
    },
    // 修改页面
    changePage(page) {
      this.filterParam.page = page;
      this.pageByPramas();
    },
    // 退货状态映射
    getRefundStatus(obj) {
      if (obj === "WAIT_REFUND") {
        return "待退款";
      }
      if (obj === "REFUND") {
        return "已退款";
      }
      if (obj === "FAILED") {
        return "退款失败";
      }
      return "无退款";
    },
    // 订单状态映射
    getOrderStatus(obj) {
      if (obj === "COMPLETE") {
        return "已完成";
      }
      if (obj === "FAILED") {
        return "已失败";
      }
      if (obj === "WAIT_SHIP") {
        return "待支付";
      }
      if (obj === "refund") {
        return "退款成功";
      }
      if (obj === "refund-fail") {
        return "退款失败";
      }
    },
    getRefund(obj) {
      if (obj == "" || obj == null || obj == undefined) {
        return 0;
      } else {
        return obj;
      }
    },
    // 点击退款
    async clickRefund(order) {
      console.log(order);
      if (order.refundStatus === "REFUND") {
        this.$message.error("该订单已退款");
        return;
      }

      // 获取可退款金额
      const data = {
        orderNo: order.orderNo,
      };
      const maxMoeny = await (await queryRefundMoney(data)).data;
      console.log(maxMoeny);

      this.$prompt("是否对该订单申请退款?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入退款金额",
        inputPattern: /^(0|([1-9][0-9]*))(\.[\d]+)?$/,
      })
        .then(({ value }) => {
          console.log("打印信息");
          console.log(order);
          console.log(value);
          this.torefund(order, value, maxMoeny);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消输入",
          });
        });
    },

    // 退款
    torefund(order, value, maxMoeny) {
      if (value > maxMoeny) {
        this.$message.error("无法大于订单总金额");
        return;
      }
      const data = {
        mchnt_order_no: order.orderNo,
        refund_amt: value,
        total_amt: order.order_money + "",
        order_time: order.order_time,
      };

      // 申请退款逻辑
      refund(data)
        .then((res) => {
          console.log(res);
          if (res.data.message == "退款成功") {
            // 退款成功，不显示消息提示
            // this.pageByPramas()
          } else {
            this.$message.error("退款失败");
          }
        })
        .catch((error) => {
          this.$message.error("退款失败");
          return;
        });
    },
    // 获取订单字典
    allcommodity() {
      allcommodity().then((res) => {
        // console.log(res)
        res = res.data;
        if (res.code !== 200) {
          return;
        }
        res.data.forEach((o) => {
          this.dictKey[o.id] = o.name;
        });
      });
    },

    // 切换订单状态搜索条件
    changeOrderState(type) {
      if (type == 0) {
        this.searchorderstate = "";
      } else if (type == 1) {
        this.searchorderstate = "COMPLETE";
      } else if (type == 2) {
        this.searchorderstate = "FAILED";
      } else if (type == 3) {
        this.searchorderstate = "WAIT_SHIP";
      } else if (type == 4) {
        this.searchorderstate = "refund";
      } else {
        this.searchorderstate = "";
      }
      // 调用查询
      this.pageByPramas();
    },
    // 设置默认时间
    setDefaultTime(picker) {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      this.value1 = [start, end];
    },
    change(o) {
      console.log(o);
      console.log(this.value1);
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始，需加1，并确保是两位数
      const day = String(date.getDate()).padStart(2, "0"); // 确保日期是两位数
      const hours = String(date.getHours()).padStart(2, "0"); // 确保小时是两位数
      const minutes = String(date.getMinutes()).padStart(2, "0"); // 确保分钟是两位数
      const seconds = String(date.getSeconds()).padStart(2, "0"); // 确保秒数是两位数

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 下载订单excel表
    downLoadExcex() {
      const h = this.$createElement;
      this.$msgbox({
        title: "提示",
        message: h("p", null, [
          h("span", { style: "color: red" }, "是否确认导出excel数据"),
        ]),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "导出中...";

            const data = {};
            if (this.value1) {
              data.start_time = this.formatDate(this.value1[0]);
              data.end_time = this.formatDate(this.value1[1]);
            }

            // 处理excel数据
            getQykExcel(data).then((res) => {
              if (res.data.message === "success") {
                const data = res.data.data;
                // 使用xlsx库的工作簿和工作表API
                const ws = xlsx.utils.aoa_to_sheet(data); // 将数组转换为工作表
                const wb = xlsx.utils.book_new(); // 创建一个新的工作簿
                xlsx.utils.book_append_sheet(wb, ws, "Sheet1"); // 将工作表添加到工作簿

                // 生成Excel文件
                const wbout = xlsx.write(wb, {
                  bookType: "xlsx",
                  type: "binary",
                });

                // 使用file-saver保存文件
                function s2ab(s) {
                  const buf = new ArrayBuffer(s.length);
                  const view = new Uint8Array(buf);
                  for (let i = 0; i < s.length; i++) {
                    view[i] = s.charCodeAt(i) & 0xff;
                  }
                  return buf;
                }

                const dataName = "权益卡订单导出数据.xlsx";
                saveAs(
                  new Blob([s2ab(wbout)], { type: "application/octet-stream" }),
                  dataName
                );
                done();
                instance.confirmButtonLoading = false;
              } else {
                this.$message.error("导出数据失败");
              }
            });
          } else {
            done();
          }
        },
      })
        .then((action) => {
          // 导出成功，不显示消息提示
        })
        .catch((action) => {
          // 取消导出，不显示消息提示
        });
    },
    // 订单状态类型映射（用于标签颜色）
    getOrderStatusType(status) {
      const statusMap = {
        COMPLETE: "success",
        SUCCESS: "success",
        FAILED: "danger",
        WAIT_SHIP: "warning",
        refund: "info",
        "refund-fail": "danger",
      };
      return statusMap[status] || "info";
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 6px;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 8px;
}

.search-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px;
  border: 1px solid #e5e7eb;
}

.search-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.search-header .search-icon {
  color: #409eff;
  font-size: 1.1rem;
  margin-right: 6px;
}

.search-header .search-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.search-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.search-input-wrapper {
  flex: 1;
  min-width: 200px;
  max-width: 250px;
}

.search-button-wrapper {
  min-width: 100px;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 8px;
}

.filter-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 6px;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-selector {
  display: flex;
  align-items: center;
  gap: 12px;
}

.selector-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selector-label {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  white-space: nowrap;
}

.status-selector {
  min-width: 150px;
}

.date-picker-wrapper {
  flex: 1;
  min-width: 300px;
}

.date-picker {
  width: 100%;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 8px;
}

.table-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
  padding: 8px 10px;
  border-bottom: 1px solid #e5e7eb;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.table-title i {
  margin-right: 6px;
  color: #409eff;
  font-size: 1.1rem;
}

.user-id {
  font-weight: 600;
  color: #409eff;
}

.agent-info {
  color: #6b7280;
}

.product-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-icon {
  color: #10b981;
  font-size: 1rem;
}

.order-no {
  font-family: "Monaco", "Menlo", monospace;
  color: #4338ca;
  font-weight: 500;
  font-size: 12px;
}

.price-text {
  font-weight: 600;
  color: #059669;
}

.refund-amount {
  font-weight: 600;
  color: #dc2626;
}

.real-name,
.address,
.car-id {
  color: #6b7280;
}

.phone-number {
  font-family: "Monaco", "Menlo", monospace;
  color: #059669;
  font-weight: 500;
}

.order-time {
  color: #6b7280;
  font-size: 12px;
}

.pay-platform {
  color: #4338ca;
  font-weight: 500;
}

.status-tag {
  font-weight: 600;
}

.action-btn {
  transition: all 0.3s ease;
}

.refund-btn:hover {
  box-shadow: 0 3px 12px rgba(239, 68, 68, 0.4);
}

/* 分页样式 */
.pagination-section {
  display: flex;
  justify-content: right;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 4px;
  }

  .search-content {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input-wrapper {
    max-width: none;
  }

  .filter-card {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .filter-selector {
    justify-content: center;
  }

  .selector-wrapper {
    flex-direction: column;
    align-items: center;
    gap: 4px;
  }

  .status-selector {
    min-width: 200px;
  }

  .date-picker-wrapper {
    min-width: auto;
  }
}
</style>
