﻿<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-card">
        <div class="header-content">
          <i class="el-icon-upload header-icon" />
          <span class="header-title">应用版本管理</span>
          <div class="version-status">
            <i class="el-icon-info" />
            <span>当前版本: v{{ newVersion.versionId || "未知" }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="content-wrapper">
      <!-- 上传表单区域 -->
      <div class="upload-section">
        <div class="upload-card">
          <div class="card-header">
            <div class="card-title">
              <i class="el-icon-upload2" />
              <span>上传新版本</span>
            </div>
          </div>

          <el-form
            ref="uploadForm"
            v-loading="loading"
            :model="uploadForm"
            :rules="rules"
            label-width="120px"
            element-loading-text="正在上传，请稍候..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(255, 255, 255, 0.8)"
          >
            <el-form-item label="版本号" prop="version_id">
              <el-input
                v-model="uploadForm.version_id"
                placeholder="请输入版本号，如：1.0.0"
                prefix-icon="el-icon-price-tag"
                clearable
              >
                <template slot="prepend">v</template>
              </el-input>
              <div class="form-tip">
                <i class="el-icon-info" />
                <span>请按照语义化版本规范填写，如：1.0.0、2.1.3</span>
              </div>
            </el-form-item>

            <el-form-item label="应用文件" prop="appFile">
              <div class="file-upload-container">
                <div
                  class="file-drop-zone"
                  :class="{ 'drag-over': isDragOver, 'has-file': selectedFile }"
                  @drop="handleFileDrop"
                  @dragover="handleDragOver"
                  @dragleave="handleDragLeave"
                  @click="triggerFileInput"
                >
                  <input
                    ref="appFile"
                    type="file"
                    accept=".apk,.ipa"
                    style="display: none"
                    @change="handleFileSelect"
                  />

                  <div v-if="!selectedFile" class="drop-content">
                    <i class="el-icon-upload" />
                    <p class="drop-text">点击选择文件或拖拽文件到此处</p>
                    <p class="drop-hint">
                      支持 .apk 和 .ipa 格式，文件大小不超过 100MB
                    </p>
                  </div>

                  <div v-else class="file-info">
                    <div class="file-icon">
                      <i class="el-icon-document" />
                    </div>
                    <div class="file-details">
                      <div class="file-name">{{ selectedFile.name }}</div>
                      <div class="file-size">
                        {{ formatFileSize(selectedFile.size) }}
                      </div>
                    </div>
                    <div class="file-actions">
                      <el-button
                        type="text"
                        icon="el-icon-close"
                        @click.stop="clearFile"
                      >
                        移除
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="loading"
                :disabled="!uploadForm.version_id || !selectedFile"
                icon="el-icon-upload"
                @click="uploadVersion('uploadForm')"
              >
                {{ loading ? "上传中..." : "开始上传" }}
              </el-button>
              <el-button
                :disabled="loading"
                @click="resetForm"
              >
                重置表单
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 版本信息区域 -->
      <div class="version-section">
        <div class="version-card">
          <div class="card-header">
            <div class="card-title">
              <i class="el-icon-info" />
              <span>当前版本信息</span>
            </div>
            <el-button
              type="text"
              icon="el-icon-refresh"
              @click="getNewVersion"
            >
              刷新
            </el-button>
          </div>

          <div class="version-content">
            <div v-if="!newVersion.versionId" class="version-empty">
              <i class="el-icon-warning-outline" />
              <p>暂无版本信息</p>
            </div>

            <div v-else class="version-info">
              <div class="info-item">
                <div class="info-label">
                  <i class="el-icon-price-tag" />
                  <span>版本号</span>
                </div>
                <div class="info-value version-tag">
                  v{{ newVersion.versionId }}
                </div>
              </div>

              <div class="info-item">
                <div class="info-label">
                  <i class="el-icon-link" />
                  <span>下载地址</span>
                </div>
                <div class="info-value">
                  <div class="download-link">
                    <span class="link-text">{{
                      newVersion.downloadAddr || "暂无下载链接"
                    }}</span>
                    <el-button
                      v-if="newVersion.downloadAddr"
                      type="text"
                      icon="el-icon-copy-document"
                      @click="copyDownloadLink"
                    >
                      复制
                    </el-button>
                  </div>
                </div>
              </div>

              <div v-if="newVersion.uploadTime" class="info-item">
                <div class="info-label">
                  <i class="el-icon-time" />
                  <span>更新时间</span>
                </div>
                <div class="info-value">
                  {{ formatTime(newVersion.uploadTime) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 上传历史 -->
        <div class="history-card">
          <div class="card-header">
            <div class="card-title">
              <i class="el-icon-time" />
              <span>上传历史</span>
            </div>
          </div>

          <div class="history-content">
            <div v-if="uploadHistory.length === 0" class="history-empty">
              <i class="el-icon-document-remove" />
              <p>暂无上传历史</p>
            </div>

            <div v-else class="history-list">
              <div
                v-for="(item, index) in uploadHistory"
                :key="index"
                class="history-item"
              >
                <div class="history-icon">
                  <i v-if="item.status === 'success'" class="el-icon-check" />
                  <i v-else class="el-icon-close" />
                </div>
                <div class="history-info">
                  <div class="history-version">v{{ item.version }}</div>
                  <div class="history-time">{{ formatTime(item.time) }}</div>
                </div>
                <div class="history-status" :class="item.status">
                  {{ item.status === "success" ? "成功" : "失败" }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { versionUpload, GetNewVersion } from "@/api/upload";
import { handleError, handleBusinessError } from "@/utils/errorHandler";

export default {
  data() {
    const validateVersionId = (_, value, callback) => {
      if (!value) {
        callback(new Error("版本号不能为空"));
        return;
      }
      // 简单的版本号格式验证
      const versionRegex = /^\d+\.\d+\.\d+$/;
      if (!versionRegex.test(value)) {
        callback(new Error("版本号格式不正确，请使用如 1.0.0 的格式"));
        return;
      }
      callback();
    };

    return {
      uploadForm: {
        version_id: "",
      },
      rules: {
        version_id: [{ validator: validateVersionId, trigger: "blur" }],
      },
      newVersion: {
        downloadAddr: "",
        versionId: "",
        uploadTime: "",
      },
      loading: false,
      selectedFile: null,
      isDragOver: false,
      uploadHistory: [],
    };
  },

  mounted() {
    this.getNewVersion();
  },

  methods: {
    uploadVersion(formName) {
      this.$refs[formName].validate((valid) => {
        if (!valid) return false;

        if (!this.selectedFile) {
          this.$message.error("请选择要上传的文件");
          return false;
        }

        this.loading = true;
        const formData = new FormData();
        formData.append("file", this.selectedFile);
        formData.append("version_id", this.uploadForm.version_id);

        versionUpload(formData)
          .then((res) => {
            if (handleBusinessError(res, "上传失败")) {
              // 版本上传成功，不显示消息提示
              this.addToHistory("success");
              this.getNewVersion();
              this.resetForm();
            } else {
              this.addToHistory("failed");
            }
          })
          .catch((error) => {
            console.error("上传失败:", error);
            handleError(error, "上传失败，请重试");
            this.addToHistory("failed");
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },

    getNewVersion() {
      GetNewVersion()
        .then((res) => {
          res = res.data;
          if (res.code === 200 && res.data) {
            this.newVersion = {
              ...res.data,
              uploadTime: res.data.uploadTime || new Date(),
            };
          }
        })
        .catch((error) => {
          console.error("获取版本信息失败:", error);
        });
    },

    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        this.validateAndSetFile(file);
      }
    },

    handleFileDrop(event) {
      event.preventDefault();
      this.isDragOver = false;

      const files = event.dataTransfer.files;
      if (files.length > 0) {
        this.validateAndSetFile(files[0]);
      }
    },

    handleDragOver(event) {
      event.preventDefault();
      this.isDragOver = true;
    },

    handleDragLeave() {
      this.isDragOver = false;
    },

    validateAndSetFile(file) {
      // 文件类型验证
      const allowedTypes = [".apk", ".ipa"];
      const fileExtension = "." + file.name.split(".").pop().toLowerCase();

      if (!allowedTypes.includes(fileExtension)) {
        this.$message.error("文件格式不支持，请选择 .apk 或 .ipa 文件");
        return;
      }

      // 文件大小验证 (100MB)
      const maxSize = 100 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$message.error("文件大小不能超过 100MB");
        return;
      }

      this.selectedFile = file;
    },

    triggerFileInput() {
      this.$refs.appFile.click();
    },

    clearFile() {
      this.selectedFile = null;
      this.$refs.appFile.value = "";
    },

    resetForm() {
      this.uploadForm.version_id = "";
      this.clearFile();
      this.$refs.uploadForm.clearValidate();
    },

    copyDownloadLink() {
      if (!this.newVersion.downloadAddr) return;

      navigator.clipboard
        .writeText(this.newVersion.downloadAddr)
        .then(() => {
          // 下载链接已复制到剪贴板，不显示消息提示
        })
        .catch(() => {
          // 降级方案
          const input = document.createElement("input");
          input.value = this.newVersion.downloadAddr;
          document.body.appendChild(input);
          input.select();
          document.execCommand("copy");
          document.body.removeChild(input);
          // 下载链接已复制到剪贴板，不显示消息提示
        });
    },

    formatFileSize(bytes) {
      if (bytes === 0) return "0 Bytes";
      const k = 1024;
      const sizes = ["Bytes", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },

    formatTime(time) {
      if (!time) return "";
      const date = new Date(time);
      return date.toLocaleString("zh-CN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    },

    addToHistory(status) {
      const historyItem = {
        version: this.uploadForm.version_id,
        time: new Date(),
        status: status,
      };
      this.uploadHistory.unshift(historyItem);
      // 只保留最近10条记录
      if (this.uploadHistory.length > 10) {
        this.uploadHistory = this.uploadHistory.slice(0, 10);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 6px;
  background: transparent;
}

// 页面头部
.page-header {
  margin-bottom: 8px;
}

.header-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .header-icon {
      font-size: 20px;
      color: #409eff;
      margin-right: 12px;
    }

    .header-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: #374151;
      flex: 1;
    }

    .version-status {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.8rem;
      background: rgba(16, 185, 129, 0.1);
      color: #059669;

      i {
        font-size: 0.8rem;
      }
    }
  }
}

// 内容包装器
.content-wrapper {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 12px;
  align-items: start;
}

// 上传区域
.upload-section {
  .upload-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
  }
}

// 卡片通用样式
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);

  .card-title {
    display: flex;
    align-items: center;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;

    i {
      margin-right: 6px;
      color: #409eff;
    }
  }

  .refresh-btn {
    color: #6b7280;
    padding: 4px 8px;

    &:hover {
      color: #409eff;
    }
  }
}

// 表单样式
.upload-form {
  padding: 10px;

  .form-tip {
    display: flex;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: #6b7280;

    i {
      margin-right: 4px;
      color: #93c5fd;
    }
  }

  .file-upload-container {
    .file-drop-zone {
      border: 2px dashed #d1d5db;
      border-radius: 8px;
      padding: 40px 20px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fafbfc;

      &:hover,
      &.drag-over {
        border-color: #409eff;
        background: rgba(64, 158, 255, 0.05);
      }

      &.has-file {
        border-color: #10b981;
        background: rgba(16, 185, 129, 0.05);
        padding: 6px;
      }

      .drop-content {
        i {
          font-size: 48px;
          color: #9ca3af;
          margin-bottom: 12px;
        }

        .drop-text {
          font-size: 1rem;
          color: #374151;
          margin: 0 0 8px 0;
          font-weight: 500;
        }

        .drop-hint {
          font-size: 0.8rem;
          color: #6b7280;
          margin: 0;
        }
      }

      .file-info {
        display: flex;
        align-items: center;
        text-align: left;

        .file-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          margin-right: 16px;

          i {
            font-size: 24px;
          }
        }

        .file-details {
          flex: 1;

          .file-name {
            font-weight: 600;
            color: #374151;
            margin-bottom: 4px;
            word-break: break-all;
          }

          .file-size {
            font-size: 0.8rem;
            color: #6b7280;
          }
        }

        .file-actions {
          .remove-btn {
            color: #ef4444;
            padding: 4px 8px;

            &:hover {
              background: rgba(239, 68, 68, 0.1);
            }
          }
        }
      }
    }
  }

  .form-actions {
    margin-top: 32px;
    margin-bottom: 0;

    .upload-btn {
      background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
      border: none;
      border-radius: 8px;
      padding: 12px 24px;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        box-shadow: 0 3px 12px rgba(64, 158, 255, 0.4);
      }

      &:disabled {
        background: #e5e7eb;
        color: #9ca3af;
        transform: none;
        box-shadow: none;
      }
    }

    .reset-btn {
      margin-left: 12px;
      border-radius: 8px;
      border: 1px solid #d1d5db;
      color: #6b7280;

      &:hover {
        background: #f3f4f6;
        border-color: #9ca3af;
      }
    }
  }
}

// 版本信息区域
.version-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.version-card,
.history-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.version-content,
.history-content {
  padding: 10px;
}

.version-empty,
.history-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #9ca3af;

  i {
    font-size: 48px;
    margin-bottom: 12px;
  }

  p {
    margin: 0;
    font-size: 0.8rem;
  }
}

.version-info {
  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      display: flex;
      align-items: center;
      font-weight: 500;
      color: #374151;

      i {
        margin-right: 6px;
        color: #409eff;
        width: 16px;
      }
    }

    .info-value {
      font-weight: 400;
      color: #6b7280;
      text-align: right;
      max-width: 60%;

      &.version-tag {
        background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
        color: white;
        padding: 4px 12px;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 600;
      }

      .download-link {
        display: flex;
        align-items: center;
        gap: 8px;

        .link-text {
          word-break: break-all;
          font-size: 0.8rem;
        }

        .copy-btn {
          color: #409eff;
          padding: 4px;
          font-size: 12px;

          &:hover {
            background: rgba(64, 158, 255, 0.1);
          }
        }
      }
    }
  }
}

.history-list {
  .history-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);

    &:last-child {
      border-bottom: none;
    }

    .history-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      i {
        font-size: 0.8rem;
      }

      i.el-icon-check {
        color: #10b981;
        background: rgba(16, 185, 129, 0.1);
      }

      i.el-icon-close {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
      }
    }

    .history-info {
      flex: 1;

      .history-version {
        font-weight: 600;
        color: #374151;
        margin-bottom: 2px;
      }

      .history-time {
        font-size: 12px;
        color: #9ca3af;
      }
    }

    .history-status {
      font-size: 12px;
      font-weight: 600;
      padding: 2px 8px;
      border-radius: 8px;

      &.success {
        color: #10b981;
        background: rgba(16, 185, 129, 0.1);
      }

      &.failed {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .content-wrapper {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .version-section {
    order: -1;
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 4px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .version-status {
      align-self: flex-end;
    }
  }

  .upload-form {
    padding: 6px;

    .file-drop-zone {
      padding: 30px 15px;

      .file-info {
        flex-direction: column;
        text-align: center;

        .file-icon {
          margin: 0 0 12px 0;
        }

        .file-details {
          margin-bottom: 12px;
        }
      }
    }
  }

  .version-content,
  .history-content {
    padding: 6px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    .info-value {
      max-width: 100%;
      text-align: left;
    }
  }
}
</style>
