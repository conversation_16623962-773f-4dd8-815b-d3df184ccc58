<template>
  <view class="settings-container">
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 用户信息卡片 -->
      <view class="user-card">
        <view class="card-title">基本信息</view>
        <!-- 头像和手机号区域 -->
        <view class="avatar-section">
          <view class="avatar-wrapper" @click="chooseImage">
            <image v-if="userInfo.headPic === ''" :src="`${config.BASE_URL}/static/photo.png`" class="user-avatar">
            </image>
            <image v-else :src="userInfo.headPic" class="user-avatar"></image>
            <view class="avatar-badge">
              <uni-icons type="camera" size="16" color="#fff" class="camera-icon"></uni-icons>
            </view>
          </view>
          <view class="user-info">
            <view class="info-label">手机号</view>
            <view class="phone-number">{{
              userInfo.phone || "未绑定手机"
            }}</view>
          </view>
        </view>

        <!-- 用户名编辑 -->
        <view class="info-edit-group">
          <view class="edit-label">用户名</view>
          <view class="input-wrapper">
            <view class="input-icon">
              <uni-icons type="person" size="16" color="#999" class="person-icon"></uni-icons>
            </view>
            <input class="form-input" type="text" placeholder="请输入用户名" v-model="editUserName"
              placeholder-class="input-placeholder" />
            <button class="save-btn" @click="saveUserName" :disabled="isSaveDisabled">
              <text>保存</text>
            </button>
          </view>
        </view>
      </view>

      <!-- 代理商信息卡片 -->
      <view class="info-card">
        <view class="card-title">代理商信息</view>
        <view class="info-grid">
          <view class="info-item">
            <view class="info-icon">
              <uni-icons type="star" size="20" color="#00d4aa" class="star-icon"></uni-icons>
            </view>
            <view class="info-content">
              <view class="info-label">代理等级</view>
              <view class="info-value level">{{
                rankDict[userInfo.adminRabk] || "代理商"
              }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view class="action-section">
        <button class="logout-btn" @click="logout">
          <text>退出登录</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import {
  showInformations,
  setUserName,
  uploadImage,
} from "@/subpackages/proxy/api/proxyUser.js";
import { resetInfo, clearProxyLoginStatus } from "@/utils/auth.js";
import { mapGetters } from "vuex";
import config from "@/utils/config.js";

export default {
  data() {
    return {
      config,
      isSend: false,
      rankDict: ["", "1级代理", "2级代理", "总代理"],
      headPicUrl: "", // 新头像URL
      editUserName: "", // 编辑中的用户名
    };
  },
  computed: {
    ...mapGetters("proxyUser", ["userInfo"]),
    // 计算保存按钮是否应该禁用
    isSaveDisabled() {
      return this.isSend || !this.editUserName || !this.editUserName.trim();
    },
  },
  watch: {
    // 监听userInfo变化，初始化编辑用户名
    userInfo: {
      handler(newVal) {
        if (newVal && newVal.userName) {
          this.editUserName = newVal.userName;
        }
      },
      immediate: true,
    },
    isSend(c) {
      if (c) {
        uni.showLoading({
          title: "加载中",
          mask: true,
        });
      } else {
        uni.hideLoading();
      }
    },
  },
  methods: {
    // 保存用户名
    saveUserName() {
      if (!this.editUserName.trim()) {
        uni.showToast({
          title: "请输入用户名",
          icon: "error",
        });
        return;
      }

      if (this.isSend) {
        return;
      }

      this.isSend = true;
      const data = {
        username: this.editUserName.trim(),
        headPic: this.userInfo.headPic, // 保持头像不变
      };

      setUserName(data)
        .then((res) => {
          if (res && res.data.code === 200) {
            uni.showToast({
              title: "用户名更新成功",
              icon: "success",
            });
            // 更新用户信息
            this.$store.dispatch("proxyUser/getUserInfo");
          } else {
            throw new Error(res.data.message || "用户名更新失败");
          }
        })
        .catch((err) => {
          console.error("用户名更新失败:", err);
          uni.showToast({
            title: err.message || "用户名更新失败",
            icon: "error",
          });
        })
        .finally(() => {
          this.isSend = false;
        });
    },
    logout() {
      uni.showModal({
        title: "提示",
        content: "确定要退出代理端吗？",
        success: (res) => {
          if (res.confirm) {
            // 清除代理端登录状态
            clearProxyLoginStatus();
            this.$store.commit("proxyUser/DEFAULTUSERINFO");

            // 跳转到统一登录页面
            uni.reLaunch({
              url: "/pages/unifiedLogin/unifiedLogin",
            });
          }
        },
      });
    },
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.uploadImageFile(tempFilePath);
        },
      });
    },
    uploadImageFile(filePath) {
      if (this.isSend) {
        return;
      }
      this.isSend = true;
      uploadImage(filePath)
        .then((res) => {
          if (res.data.code === 200) {
            this.headPicUrl = res.data.data;
            // 直接更新用户头像
            const data = {
              username: this.userInfo.userName, // 保持用户名不变
              headPic: res.data.data,
            };
            return setUserName(data);
          } else {
            throw new Error("图片上传失败");
          }
        })
        .then((res) => {
          if (res && res.data.code === 200) {
            uni.showToast({
              title: "头像更新成功",
              icon: "success",
            });
            this.$store.dispatch("proxyUser/getUserInfo");
          } else {
            throw new Error("头像更新失败");
          }
        })
        .catch((err) => {
          console.error("上传失败:", err);
          uni.showToast({
            title: err.message || "头像更新失败",
            icon: "error",
          });
        })
        .finally(() => {
          this.isSend = false;
        });
    },
  },
  onLoad() {
    // 检查页面访问权限
    const { checkCurrentPageAccess } = require("@/utils/routeGuard.js");
    const { handlePagePermissionCheck } = require("@/utils/permissionChecker.js");

    // 使用路由守卫检查权限
    if (!checkCurrentPageAccess()) {
      return; // 如果无权限访问，路由守卫会自动重定向
    }

    // 严格的权限检查：基于adminRank的代理端页面权限验证
    if (!handlePagePermissionCheck("proxy", "代理端个人信息编辑页面")) {
      return; // 权限检查失败，会自动重定向
    }

    console.log("[ProxySetUserInfo] 权限检查通过，允许访问代理端编辑页面");
  },
  onHide() {
    uni.hideLoading();
  },
};
</script>

<style lang="scss" scoped>
.settings-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 主要内容 */
.main-content {
  padding: 20px;
}

/* 用户信息卡片 */
.user-card {
  background: #fff;
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.08);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  letter-spacing: 0.3px;
}

/* 头像区域 */
.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.avatar-wrapper {
  position: relative;
  margin-right: 20px;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.user-avatar {
  width: 70px;
  height: 70px;
  border-radius: 20px;
  border: 3px solid #00d4aa;
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.2);
}

.avatar-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: #00d4aa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
}

.user-info {
  flex: 1;
}

.info-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.phone-number {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  letter-spacing: 0.3px;
}

/* 信息编辑组 */
.info-edit-group {
  margin-bottom: 20px;
}

.edit-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  min-height: 44px;
  padding: 0 12px 0 40px;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.form-input {
  flex: 1;
  font-size: 15px;
  color: #333;
  font-weight: 500;
  border: none;
  background: transparent;
  outline: none;
  padding: 0;
  margin-right: 10px;
}

.input-placeholder {
  color: #999;
  font-size: 15px;
}

.save-btn {
  background: #00d4aa;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  min-width: 50px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:disabled {
    background: #ccc;
    color: #999;
  }

  &:not(:disabled):active {
    transform: scale(0.95);
    background: #00b894;
  }

  text {
    color: inherit;
    font-size: 12px;
    font-weight: 500;
  }
}

/* 信息卡片 */
.info-card {
  background: #fff;
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.08);
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 15px;
  transition: all 0.3s ease;
}

.info-icon {
  width: 40px;
  height: 40px;
  background: rgba(0, 212, 170, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.info-content {
  flex: 1;
}

.info-value {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  letter-spacing: 0.3px;

  &.level {
    color: #00d4aa;
    font-size: 18px;
  }
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
}

.action-section button {
  width: 100%;
  height: 50px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-sizing: border-box;
  letter-spacing: 1px;

  &:active {
    transform: scale(0.98);
  }

  text {
    color: inherit;
    font-size: 16px;
    font-weight: 500;
  }
}

.logout-btn {
  background: #fff;
  color: #ff6b6b;
  border: 2px solid #ff6b6b;
  display: flex;
  justify-content: center;
  align-items: center;

  &:active {
    background: #ff6b6b;
    color: #fff;
  }
}

/* Custom icon styles */
.camera-icon {
  color: #fff;
  font-size: 12px;
}

.person-icon {
  color: #999 !important;
  font-size: 16px !important;
}

.star-icon {
  color: #00d4aa;
  font-size: 20px;
}
</style>
