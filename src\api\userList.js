import {
  get,
  post
} from '@/utils/requestMethod'

const pageByPramas = function(data) {
  return get('userinformation/pageByPramas', data)
}

const getAllUserInfo = function(data) {
  return post('/gly/getAllUserInfo', data)
}

// 修改用户
const updT = function(data) {
  return post('userinformation/updT', data)
}

// 根据phone查询数据
const getUserInfoByPhone = function(data) {
  return post('/gly/getUserInfoByPhone', data)
}

export {
  pageByPramas,
  updT,
  getUserInfoByPhone,
  getAllUserInfo
}
