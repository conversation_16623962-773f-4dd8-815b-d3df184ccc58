<template>
  <view class="unified-login-container">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <view class="nav-left" @click="goBack">
        <text class="custom-icon" style="color: #333; font-size: 18px">‹</text>
      </view>
      <view class="nav-title">登录</view>
      <view class="nav-right"> </view>
    </view>

    <!-- 主内容区域 -->
    <view class="main-content">
      <!-- ETC品牌标识 -->
      <view class="etc-brand">
        <!-- 品牌文字 -->
        <view class="brand-text">
          <text class="brand-name">E卡畅通</text>
        </view>
      </view>

      <!-- 描述文字 -->
      <view class="description">
        {{
          isProxyMode
            ? "欢迎使用E卡畅通代理小程序"
            : "登录小程序后，享受更多服务"
        }}
      </view>
    </view>
    <!-- 底部按钮区域 -->
    <view class="bottom-section">
      <!-- 代理端表单（如果是代理模式） -->
      <view v-if="isProxyMode" class="proxy-form">
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <uni-icons type="phone" size="18" color="#999" class="custom-icon"></uni-icons>
            </view>
            <input class="form-input" maxlength="11" placeholder="请输入手机号" v-model="loginForm.phone" type="number"
              placeholder-class="input-placeholder" />
          </view>
        </view>
        <view class="input-group">
          <view class="input-wrapper verification-wrapper">
            <view class="input-icon">
              <uni-icons type="locked" size="18" color="#999" class="custom-icon"></uni-icons>
            </view>
            <input class="form-input verification-input" maxlength="6" placeholder="请输入验证码"
              v-model="loginForm.verificationCode" type="number" placeholder-class="input-placeholder" />
            <view class="verification-btn" :class="{ disabled: verification || isMessage }" @click="sendVerification">
              <text v-if="!verification">获取验证码</text>
              <text v-else>{{ verificationSecond }}s后重发</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 登录按钮 -->
      <view class="login-btn-container">
        <!-- 用户端登录按钮 - 根据协议状态动态切换 -->
        <button v-if="!isProxyMode && !agreement" class="login-btn" :class="{ loading: isMessage }"
          @click="handleUserLogin">
          <text v-if="!isMessage">手机号快捷登录</text>
          <text v-else>登录中...</text>
        </button>

        <!-- 协议已勾选时显示的授权按钮 -->
        <button v-if="!isProxyMode && agreement" class="login-btn" :class="{ loading: isMessage }"
          open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
          <text v-if="!isMessage">手机号快捷登录</text>
          <text v-else>登录中...</text>
        </button>

        <button v-if="isProxyMode" class="login-btn" :class="{ loading: isMessage }" @click="checkLogin">
          <text v-if="!isMessage">立即登录</text>
          <text v-else>登录中...</text>
        </button>
      </view>

      <!-- 切换登录模式按钮 -->
      <view class="switch-section">
        <view class="switch-btn" @click="toggleLoginMode">
          <text class="switch-text">{{
            isProxyMode ? "切换到用户登录" : "切换到代理登录"
          }}</text>
        </view>
      </view>
      <!-- 协议勾选 - 通用协议区域 -->
      <view class="agreement-section" :class="{ 'shake-animation': isShaking }">
        <view class="checkbox-wrapper" @click="agreement = !agreement">
          <view class="checkbox" :class="{ checked: agreement }">
            <uni-icons v-if="agreement" type="checkmarkempty" size="10" color="#fff" class="custom-icon"></uni-icons>
          </view>
          <text class="agreement-text" :class="{ 'highlight-text': isShaking }">
            登录即同意
            <text class="link-text" @click.stop="openUserProtocol">《用户协议》</text>
            和
            <text class="link-text" @click.stop="openPrivacyProtocol">《隐私政策》</text>
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { validPhone, validCode } from "@/utils/valid.js";
import {
  sendCode as userSendCode,
  checkLogin as userCheckLogin,
} from "@/api/login.js";
import {
  sendCode as proxySendCode,
  checkLogin as proxyCheckLogin,
} from "@/subpackages/proxy/api/proxyUser.js";
import {
  setToken,
  setUserId,
  getUserId,
  setProxyLoginStatus,
  resetInfo,
  clearProxyLoginStatus,
  setUserToken,
  setProxyToken,
  setProxyUserId,
  resetUserInfo,
  resetProxyInfo,
  setScanLoginFlag,
} from "@/utils/auth.js";
import { mapActions } from "vuex";
import {
  getPhoneNoInfo,
  userPhoneLogin,
} from "@/api/user.js";
import { wxlogin } from "@/api/login.js";

let timer;

export default {
  data() {
    return {
      isProxyMode: false, // 默认为用户登录模式
      verification: false,
      verificationSecond: 0,
      loginForm: {
        phone: "",
        verificationCode: "",
        adminRabk: "0",
      },
      isMessage: false,
      agreement: false,
      isShaking: false, // 添加协议区域闪烁状态
    };
  },

  onLoad(options) {
    // 支持通过URL参数设置登录模式
    if (options.mode === "proxy") {
      this.isProxyMode = true;
      this.loginForm.adminRabk = "1";
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      // 检查是否可以返回上一页
      const pages = getCurrentPages();
      if (pages.length > 1) {
        // 有上一页，返回上一页
        uni.navigateBack();
      } else {
        // 没有上一页，跳转到首页
        uni.switchTab({
          url: "/pages/my/my",
        });
      }
    },

    // 切换登录模式
    toggleLoginMode() {
      this.isProxyMode = !this.isProxyMode;
      // 切换模式时重置表单
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.loginForm = {
        phone: "",
        verificationCode: "",
        adminRabk: this.isProxyMode ? "1" : "0",
      };
      this.verification = false;
      this.verificationSecond = 0;
      this.agreement = false;
      this.isShaking = false; // 重置闪烁状态
      clearInterval(timer);
    },

    // 触发协议区域闪烁提醒
    triggerAgreementShake() {
      this.isShaking = true;
      setTimeout(() => {
        this.isShaking = false;
      }, 1000); // 1秒后停止闪烁
    },

    // 发送验证码
    sendVerification() {
      if (this.verification === true || this.isMessage === true) {
        return;
      }

      if (!validPhone(this.loginForm.phone)) {
        uni.showToast({
          title: "请输入正确的手机号",
          icon: "none",
        });
        return;
      }

      const data = {
        phone: this.loginForm.phone,
        invitationCode: uni.getStorageSync("invitationCode"),
        adminRabk: this.isProxyMode ? 1 : 0,
      };

      this.isMessage = true;

      // 根据模式选择不同的API
      const sendCodeApi = this.isProxyMode ? proxySendCode : userSendCode;

      sendCodeApi(data)
        .then((res) => {
          if (res.data.code === 200) {
            uni.showToast({
              title: "验证码已发送",
              icon: "success",
            });

            this.startCountdown();
          } else {
            uni.showToast({
              title: res.data.message || "发送失败",
              icon: "error",
            });
          }
        })
        .catch((err) => {
          uni.showToast({
            title: "网络错误",
            icon: "error",
          });
        })
        .finally(() => {
          this.isMessage = false;
        });
    },

    // 开始倒计时
    startCountdown() {
      this.verification = true;
      this.verificationSecond = 59;
      clearInterval(timer);
      timer = setInterval(() => {
        this.verificationSecond = this.verificationSecond - 1;
        if (this.verificationSecond <= 0) {
          clearInterval(timer);
          this.verification = false;
        }
      }, 1000);
    }, // 代理端登录（手机号+验证码）
    checkLogin() {
      // 仅在代理端模式下才执行
      if (!this.isProxyMode) {
        return;
      }

      if (!this.agreement) {
        // 触发协议区域闪烁提醒，不显示toast
        this.triggerAgreementShake();
        return;
      }

      if (this.isMessage) {
        return;
      }

      // 验证表单
      if (!validPhone(this.loginForm.phone)) {
        uni.showToast({
          icon: "error",
          title: "手机号格式有误",
        });
        return;
      }

      if (!validCode(this.loginForm.verificationCode)) {
        uni.showToast({
          icon: "error",
          title: "验证码格式有误",
        });
        return;
      }

      const data = {
        ...this.loginForm,
        adminRabk: "1", // 代理端固定为1
      };

      this.isMessage = true;

      // 代理端登录
      proxyCheckLogin(data)
        .then((res) => {
          if (res.data.code !== 200) {
            uni.showToast({
              icon: "error",
              title: "登录失败",
            });
            return;
          }

          const token = res.data.data.token;
          const userId = res.data.data.userId;

          // 清除之前的登录信息（邀请码会被自动保留）
          resetInfo();

          // 设置代理端登录信息（使用独立的代理端token存储）
          setProxyToken(token);
          setProxyUserId(userId);
          setProxyLoginStatus(true);

          // 同时设置普通用户登录状态，确保 isLogin() 返回 true
          setToken(token);
          setUserId(userId);

          setScanLoginFlag(); // 设置扫码登录标识，保护登录状态

          // 登录成功后立即获取用户信息，跳过复杂的登录状态检查
          this["proxyUser/getUserInfo"]({ skipComplexCheck: true })
            .then(() => {
              console.log("[UnifiedLogin] 代理端用户信息获取成功");
            })
            .catch((error) => {
              console.log(
                "[UnifiedLogin] 代理端用户信息获取失败，但不影响登录:",
                error.message
              );
            });

          uni.showToast({
            title: "代理端登录成功",
            icon: "success",
          });

          // 跳转到代理端首页
          setTimeout(() => {
            uni.reLaunch({
              url: "/pages/proxyIndex/index",
            });
          }, 1000);
        })
        .finally(() => {
          this.isMessage = false;
        });
    },

    // 处理用户端登录按钮点击（仅在未勾选协议时调用）
    handleUserLogin() {
      if (!this.agreement) {
        // 触发协议区域闪烁提醒，不显示toast
        this.triggerAgreementShake();
        return;
      }
    },

    // 直接触发授权流程
    triggerAuthDirectly() {
      // 在小程序环境中，我们需要通过其他方式触发授权
      // 由于无法直接模拟点击，我们改为直接调用微信API
      uni.getUserProfile({
        desc: "获取手机号用于登录",
        success: (res) => {
          // 这里处理用户信息，但我们主要需要的是手机号授权
          // 所以直接跳转到手机号授权流程
          this.requestPhoneNumber();
        },
        fail: (err) => {
          console.log("获取用户信息失败:", err);
          // 如果用户拒绝，隐藏授权按钮
          this.showAuthButton = false;
        },
      });
    },

    // 请求手机号授权
    requestPhoneNumber() {
      // 由于无法直接触发隐藏按钮，我们使用一个替代方案
      // 创建一个临时的授权按钮并立即触发
      const tempButton = {
        open_type: "getPhoneNumber",
        success: this.getPhoneNumber,
        fail: () => {
          this.showAuthButton = false;
          uni.showToast({
            title: "获取手机号失败",
            icon: "none",
          });
        },
      };

      // 在这种情况下，我们需要提示用户再次点击
      this.showAuthButton = false;
      uni.showModal({
        title: "授权提示",
        content:
          "需要获取您的手机号进行登录，请点击确定后在弹出的授权框中选择允许",
        confirmText: "确定",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            // 用户确认后，重新显示授权按钮并提示用户点击
            this.showAuthButton = true;
            uni.showToast({
              title: "请点击登录按钮进行授权",
              icon: "none",
              duration: 2000,
            });
          }
        },
      });
    },

    ...mapActions(["user/getUserInfo", "proxyUser/getUserInfo"]),

    // 微信授权获取手机号登录（用户端）
    getPhoneNumber(e) {
      console.log("获取手机号事件:", e);

      if (e.detail.errMsg !== "getPhoneNumber:ok") {
        uni.showToast({
          title: "获取手机号失败",
          icon: "none",
        });
        return;
      }

      // 显示加载中
      this.isMessage = true;
      uni.showLoading({
        title: "登录中",
        mask: true,
      });

      // 直接使用微信登录进行授权
      uni.login({
        provider: "weixin",
        success: (loginRes) => {
          console.log("微信登录成功:", loginRes);

          // 准备获取手机号的参数
          const params = {
            code: e.detail.code, // 手机号获取的code
            loginCode: loginRes.code, // 登录的code
            encryptedData: e.detail.encryptedData,
            iv: e.detail.iv,
          };
          console.log("发送获取手机号请求参数:", params);

          // 获取手机号
          this.retryRequest(() => getPhoneNoInfo(params))
            .then((res) => {
              console.log("获取手机号响应:", res);
              if (!res.data || res.data.code !== 200) {
                throw new Error(res.data?.message || "获取手机号失败");
              }

              let phone;
              // 如果后端返回的是加密的手机号数据
              if (
                typeof res.data.data === "object" &&
                res.data.data.phoneNumber
              ) {
                phone = res.data.data.phoneNumber;
              }
              // 如果后端直接返回手机号
              else if (
                typeof res.data.data === "string" &&
                res.data.data !== "null"
              ) {
                phone = res.data.data;
              } else {
                throw new Error("未能获取到有效的手机号");
              }

              console.log("获取到手机号:", phone);

              // 获取邀请码
              const invitationCode = uni.getStorageSync("invitationCode");
              console.log("[UnifiedLogin] 获取到的邀请码:", invitationCode);

              // 使用手机号登录，添加邀请码参数
              return userPhoneLogin({
                phone: phone,
                loginCode: loginRes.code, // 添加登录code
                invitationCode: invitationCode || '', // 添加邀请码参数
              });
            })
            .then((res) => {
              console.log("手机号登录响应:", res);
              if (!res.data || res.data.code !== 200) {
                throw new Error(res.data?.message || "登录失败");
              }

              if (!res.data.data?.token) {
                throw new Error("登录返回数据格式错误");
              }

              // 清除之前的登录信息（邀请码会被自动保留）
              resetInfo();

              // 保存用户端登录信息（使用独立的用户端token存储）
              setUserToken(res.data.data.token);
              setUserId(res.data.data.userId);
              clearProxyLoginStatus(); // 确保清除代理端标识

              // 保存userId用于后续使用
              const currentUserId = res.data.data.userId;

              // 第三步：调用wxlogin接口
              console.log("开始调用wxlogin接口");
              return wxlogin({
                code: loginRes.code,
                userId: currentUserId
              }).then((wxLoginRes) => {
                console.log("wxlogin响应:", wxLoginRes);
                if (!wxLoginRes.data || wxLoginRes.data.code !== 200) {
                  console.warn("wxlogin调用失败，但不影响登录流程:", wxLoginRes.data?.message);
                }
                // 返回userId供后续使用
                return currentUserId;
              });
            })
            .then((userId) => {
              // 注意：邀请码已经在userPhoneLogin接口中处理，这里不需要重复处理
              console.log("[UnifiedLogin] 登录完成，用户ID:", userId);
              const invitation = uni.getStorageSync("invitationCode");
              if (invitation) {
                console.log("[UnifiedLogin] 邀请码已在登录接口中处理:", invitation);
              }

              // 刷新用户信息
              this["user/getUserInfo"]();

              uni.showToast({
                title: "登录成功",
                icon: "success",
              });

              // 跳转到用户端首页
              setTimeout(() => {
                uni.reLaunch({
                  url: "/pages/my/my",
                });
              }, 1000);
            })
            .catch((err) => {
              console.error("登录错误:", err);
              console.error("错误详情:", JSON.stringify(err, null, 2));

              let errorMessage = "登录失败";
              if (err.message) {
                if (err.message.includes("HTTP")) {
                  errorMessage = "网络请求失败，请检查网络连接";
                } else if (err.message.includes("timeout")) {
                  errorMessage = "请求超时，请重试";
                } else if (err.message.includes("获取手机号失败")) {
                  errorMessage = "获取手机号失败，请重试";
                } else {
                  errorMessage = err.message;
                }
              }

              uni.showToast({
                title: errorMessage,
                icon: "error",
                duration: 3000,
              });
            })
            .finally(() => {
              this.isMessage = false;
              uni.hideLoading();
            });
        },
        fail: (err) => {
          console.error("微信登录失败:", err);
          this.isMessage = false;
          uni.hideLoading();
          uni.showToast({
            title: "微信登录失败",
            icon: "error",
          });
        },
      });
    },

    // 打开用户协议
    openUserProtocol() {
      uni.navigateTo({
        url: "/subpackages/common/pages/userProtocol/userProtocol",
      });
    },

    // 打开隐私政策
    openPrivacyProtocol() {
      uni.navigateTo({
        url: "/subpackages/common/pages/privacyPolicy/privacyPolicy",
      });
    },

    // 网络重试函数
    async retryRequest(requestFn, maxRetries = 3) {
      for (let i = 0; i < maxRetries; i++) {
        try {
          const result = await requestFn();
          return result;
        } catch (error) {
          console.warn(`请求尝试 ${i + 1}/${maxRetries} 失败:`, error);
          if (i === maxRetries - 1) {
            throw error;
          }
          // 等待一秒后重试
          await new Promise((resolve) => setTimeout(resolve, 1000 * (i + 1)));
        }
      }
    },
  },

  onUnload() {
    // 清理定时器
    clearInterval(timer);
  },
};
</script>

<style lang="scss" scoped>
.unified-login-container {
  height: 100vh;
  width: 100vw;
  background: #f5f5f5;
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 导航栏样式 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 20px 10px 20px;
  background: #f5f5f5;
  z-index: 10;
  position: relative;
}

.nav-left,
.nav-right {
  width: 40px;
  display: flex;
  align-items: center;
}

.nav-left {
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:active {
    background-color: rgba(0, 0, 0, 0.1);
  }

  .custom-icon {
    font-size: 20px !important;
    font-weight: bold;
  }
}

.nav-right {
  justify-content: flex-end;
  position: relative;
}

.nav-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 40px;
  margin-top: -50px;
}

/* ETC品牌标识 */
.etc-brand {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

/* 品牌文字 */
.brand-text {
  display: flex;
  align-items: baseline;
  gap: 5px;
}

.brand-name {
  font-size: 48px;
  font-weight: bold;
  color: #333;
  letter-spacing: 2px;
}

/* 描述文字 */
.description {
  font-size: 16px;
  color: #999;
  text-align: center;
  margin-bottom: 40px;
}

/* 底部区域 */
.bottom-section {
  padding: 0 40px 40px 40px;
  flex-shrink: 0;
}

/* 代理端表单样式 */
.proxy-form {
  margin-bottom: 20px;
}

.input-group {
  margin-bottom: 16px;
}

.input-wrapper {
  position: relative;
  background: #fff;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #00d4aa;
}

.verification-wrapper {
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 12px 0 40px;
  font-size: 15px;
  border: none;
  background: transparent;
  color: #333;
  box-sizing: border-box;
}

.verification-input {
  flex: 1;
  padding-right: 110px;
}

.input-placeholder {
  color: #999;
}

.verification-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  padding: 5px 10px;
  background: #00d4aa;
  color: #fff;
  border-radius: 5px;
  font-size: 11px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.verification-btn.disabled {
  background: #ccc;
  color: #666;
}

/* 登录按钮 */
.login-btn-container {
  margin-bottom: 20px;
}

.login-btn {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  color: #fff;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-sizing: border-box;
  letter-spacing: 1px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-btn.loading {
  opacity: 0.8;
}

.login-btn:active {
  transform: scale(0.98);
}

/* 隐藏的授权按钮 */
.hidden-auth-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: -1;
  pointer-events: none;
}

/* 切换登录模式按钮 */
.switch-section {
  text-align: center;
  margin-bottom: 16px;
}

.switch-btn {
  display: inline-block;
  background: transparent;
  border-radius: 20px;
  padding: 6px 16px;
  transition: all 0.3s ease;
}

.switch-btn:active {
  background: #f5f5f5;
  transform: scale(0.95);
}

.switch-text {
  font-size: 13px;
  color: #666;
  line-height: 1;
}

/* 协议区域 */
.agreement-section {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

/* 协议区域闪烁动画 */
.agreement-section.shake-animation {
  animation: shake 0.5s ease-in-out 2;
}

@keyframes shake {

  0%,
  100% {
    transform: translateX(0);
  }

  25% {
    transform: translateX(-5px);
  }

  75% {
    transform: translateX(5px);
  }
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox {
  width: 14px;
  height: 14px;
  border: 1px solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.checkbox.checked {
  background: #00d4aa;
  border-color: #00d4aa;
}

.agreement-text {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  transition: color 0.3s ease;
}

/* 协议文字高亮效果 */
.agreement-text.highlight-text {
  color: #ff6b6b;
}

.link-text {
  color: #00d4aa;
}
</style>
