import {
  get,
  post
} from '@/utils/requestMethod'

const refund = function(data) {
  return post('/fuioepay/refund/', data)
}
const rejuctRefund = function(data) {
  return post('/gly/refuseRefund', data)
}

const queryRefundMoney = function(data) {
  return post('/orderqyk/searchRefundMoney', data)
}

const refundCredit = function(data) {
  return post('/fuioepay/refundCredit/', data)
}

export {
  refund,
  rejuctRefund,
  queryRefundMoney,
  refundCredit
}
