

/**
 * 验证手机号
 * @param phone 手机号码
 * @returns boolean 返回是否位合法的手机号码
 */

const validPhone = function(phone = '') {
	if(!(typeof phone === 'string')) {
		return false
	}
	return /^1[3456789]\d{9}$/g.test(phone)
}

/**
 * 验证验证码
 * @param code 验证码
 * @param num 几位
 * @returns Boolean 该验证码是否合法
 */
const validCode = function(code = '', num = 6) {
	if(!(typeof code === 'string')) {
		return false
	}
	const expStr = '^[0-9]{' + num +'}$'
	
	const exp = new RegExp(expStr)
	
	return exp.test(code)
}


export {
	validPhone,
	validCode
}