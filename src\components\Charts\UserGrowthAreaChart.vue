<template>
  <div class="chart-container">
    <canvas ref="chartCanvas"></canvas>
  </div>
</template>

<script>
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
  Legend,
  Filler
);

export default {
  name: "UserGrowthAreaChart",
  props: {
    echartData: {
      type: Array,
      default: () => [],
    },
    label: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    echartData: {
      handler() {
        this.updateChart();
      },
      deep: true,
    },
    label: {
      handler() {
        this.updateChart();
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.destroy();
    }
  },
  methods: {
    initChart() {
      const ctx = this.$refs.chartCanvas.getContext("2d");

      this.chart = new ChartJS(ctx, {
        type: "line",
        data: {
          labels: this.label.filter((item) => item !== ""),
          datasets: [
            {
              label: "新增用户",
              data: this.echartData.filter((item) => item !== ""),
              fill: true,
              backgroundColor: "rgba(59, 130, 246, 0.1)",
              borderColor: "rgb(59, 130, 246)",
              borderWidth: 2,
              pointBackgroundColor: "rgb(59, 130, 246)",
              pointBorderColor: "#fff",
              pointBorderWidth: 2,
              pointRadius: 4,
              pointHoverRadius: 6,
              tension: 0.4,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              backgroundColor: "rgba(0, 0, 0, 0.8)",
              titleColor: "#fff",
              bodyColor: "#fff",
              borderColor: "rgb(59, 130, 246)",
              borderWidth: 1,
              callbacks: {
                label: function (context) {
                  return `新增用户: ${context.parsed.y} 人`;
                },
              },
            },
          },
          scales: {
            x: {
              grid: {
                display: false,
              },
              ticks: {
                maxTicksLimit: 8,
                color: "#6b7280",
              },
            },
            y: {
              beginAtZero: true,
              grid: {
                color: "rgba(0, 0, 0, 0.05)",
              },
              ticks: {
                color: "#6b7280",
                callback: function (value) {
                  return value + " 人";
                },
              },
            },
          },
          interaction: {
            intersect: false,
            mode: "index",
          },
        },
      });
    },
    updateChart() {
      if (this.chart) {
        this.chart.data.labels = this.label.filter((item) => item !== "");
        this.chart.data.datasets[0].data = this.echartData.filter(
          (item) => item !== ""
        );
        this.chart.update();
      }
    },
  },
};
</script>

<style scoped>
.chart-container {
  position: relative;
  height: 200px;
  width: 100%;
}
</style>
