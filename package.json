{"name": "vue-admin-template", "version": "4.4.0", "description": "A vue admin template with Element UI & axios & iconfont & permission control & lint", "author": "seki <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve --open", "build": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "analyze": "vue-cli-service build --report"}, "dependencies": {"@tailwindcss/postcss7-compat": "^2.2.17", "axios": "0.18.1", "chart.js": "^3.9.1", "core-js": "3.6.5", "echarts": "^4.2.1", "element-ui": "2.13.2", "file-saver": "^2.0.5", "js-cookie": "2.2.0", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "vue": "2.6.10", "vue-json-excel": "^0.3.0", "vue-router": "3.0.6", "vuex": "3.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-service": "4.4.4", "autoprefixer": "^9.8.8", "babel-plugin-dynamic-import-node": "2.3.3", "postcss": "^7.0.39", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "^2.1.5", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "tailwindcss": "npm:@tailwindcss/postcss7-compat@^2.2.17", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}