<template>
  <div class="app-container">
    <div class="profile-card">
      <div class="profile-header">
        <div class="profile-avatar">
          <img
            v-if="userInfo.avatar"
            :src="userInfo.avatar"
            class="avatar-img"
            alt="用户头像"
          />
          <div v-else class="avatar-placeholder">
            <i class="el-icon-user-solid" />
          </div>
        </div>
        <div class="profile-info">
          <h2 class="username">{{ userInfo.name || "管理员" }}</h2>
          <p class="user-role">系统管理员</p>
        </div>
      </div>

      <div class="profile-content">
        <div class="info-section">
          <h3 class="section-title">
            <i class="el-icon-user" />
            基本信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <label>用户名</label>
              <el-input
                :value="userInfo.name || '管理员'"
                disabled
                placeholder="用户名"
              />
            </div>
            <div class="info-item">
              <label>角色权限</label>
              <el-input value="系统管理员" disabled placeholder="角色权限" />
            </div>
          </div>
        </div>

        <div class="info-section">
          <h3 class="section-title">
            <i class="el-icon-setting" />
            账户信息
          </h3>
          <div class="info-grid">
            <div class="info-item">
              <label>登录状态</label>
              <el-input value="已登录" disabled placeholder="登录状态" />
            </div>
            <div class="info-item">
              <label>系统权限</label>
              <el-input value="完全访问权限" disabled placeholder="系统权限" />
            </div>
          </div>
        </div>

        <div class="profile-note">
          <el-alert
            title="提示"
            description="当前系统暂不支持修改管理员个人信息，如需修改请联系系统开发人员。"
            type="info"
            :closable="false"
            show-icon
          >
          </el-alert>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "Profile",
  data() {
    return {
      userInfo: {
        name: "",
        avatar: "",
      },
    };
  },
  computed: {
    ...mapGetters(["name", "avatar"]),
  },
  mounted() {
    this.loadUserInfo();
  },
  methods: {
    loadUserInfo() {
      // 从store中获取用户信息
      this.userInfo = {
        name: this.name,
        avatar: this.avatar,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background: transparent;
}

.profile-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 800px;
  margin: 0 auto;
}

.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.6);

  .profile-avatar {
    margin-right: 30px;

    .avatar-img {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      border: 4px solid rgba(64, 158, 255, 0.2);
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(64, 158, 255, 0.6);
        transform: scale(1.05);
      }
    }

    .avatar-placeholder {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 40px;
      border: 4px solid rgba(64, 158, 255, 0.2);
    }
  }

  .profile-info {
    .username {
      font-size: 28px;
      font-weight: 600;
      color: #374151;
      margin: 0 0 8px 0;
    }

    .user-role {
      color: #6b7280;
      font-size: 16px;
      margin: 0;
      background: rgba(64, 158, 255, 0.1);
      padding: 4px 12px;
      border-radius: 20px;
      display: inline-block;
    }
  }
}

.profile-content {
  .info-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 20px;
      font-weight: 600;
      color: #374151;
      margin-bottom: 20px;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #409eff;
        font-size: 18px;
      }
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;

      .info-item {
        label {
          display: block;
          margin-bottom: 8px;
          font-weight: 600;
          color: #374151;
          font-size: 14px;
        }

        .el-input {
          :deep(.el-input__inner) {
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            padding: 12px 16px;
            transition: all 0.3s ease;
            background: rgba(249, 250, 251, 0.8);
            color: #6b7280;
          }
        }
      }
    }
  }

  .profile-note {
    margin-top: 30px;

    .el-alert {
      border-radius: 12px;
      border: 1px solid rgba(64, 158, 255, 0.2);
      background: rgba(64, 158, 255, 0.05);

      :deep(.el-alert__icon) {
        color: #409eff;
      }

      :deep(.el-alert__title) {
        color: #374151;
        font-weight: 600;
      }

      :deep(.el-alert__description) {
        color: #6b7280;
        margin-top: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 12px;
  }

  .profile-card {
    padding: 20px;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;

    .profile-avatar {
      margin-right: 0;
      margin-bottom: 20px;
    }
  }

  .info-grid {
    grid-template-columns: 1fr !important;
    gap: 16px;
  }
}
</style>
