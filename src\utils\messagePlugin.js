import { Message } from "element-ui";
import { handleError } from "./errorHandler";

/**
 * Vue消息插件，扩展$message方法支持自动中文化
 */
export default {
  install(Vue) {
    // 保存原始的$message方法
    const originalMessage = Vue.prototype.$message;

    // 扩展$message.error方法，自动进行中文化处理
    Vue.prototype.$message = {
      ...originalMessage,
      error: (message) => {
        // 使用我们的错误处理工具进行中文化
        handleError(message, message, true);
      },
      // 保持其他方法不变
      success: originalMessage.success,
      warning: originalMessage.warning,
      info: originalMessage.info,
      close: originalMessage.close,
      closeAll: originalMessage.closeAll,
    };

    // 提供快捷方法
    Vue.prototype.$showError = (
      error,
      defaultMessage = "操作失败，请稍后重试"
    ) => {
      handleError(error, defaultMessage);
    };
  },
};
