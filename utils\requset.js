import { getToken } from "@/utils/auth.js";
import {
  BASE_URL as ENV_BASE_URL,
  API_TIMEOUT,
  isDevelopment,
} from "@/utils/config.js";

// 使用环境配置中的BASE_URL
const BASE_URL = ENV_BASE_URL;

// 备用URL配置（可根据需要切换）
const BACKUP_URLS = {
  development: [
    "http://*************:8011",
    "http://************:8011",
    "http://7eafd214.r5.cpolar.top",
  ],
  production: [
    "https://elcxt.online:8011",
    "https://elcxt.online:8012",
    "https://elcxt.online:8019",
  ],
};

// 请求重试配置
const RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1秒
  retryDelayMultiplier: 2, // 每次重试延迟翻倍
};

// 网络状态检查
function checkNetworkStatus() {
  return new Promise((resolve) => {
    uni.getNetworkType({
      success: (res) => {
        const isConnected = res.networkType !== "none";
        resolve(isConnected);
      },
      fail: () => {
        resolve(false);
      },
    });
  });
}

// 延迟函数
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// 带重试机制的请求函数
async function requestWithRetry(requestConfig, retryCount = 0) {
  try {
    // 检查网络状态
    const isNetworkAvailable = await checkNetworkStatus();
    if (!isNetworkAvailable) {
      throw new Error("网络连接不可用");
    }
    const response = await new Promise((resolve, reject) => {
      uni.request({
        ...requestConfig,
        success: (res) => {
          console.log("网络请求原始响应:", res);
          resolve(res);
        },
        fail: (err) => {
          console.error("网络请求失败:", err);
          reject(new Error(`请求失败: ${err.errMsg || "网络错误"}`));
        },
      });
    });

    // 检查响应状态
    if (response && response.statusCode >= 200 && response.statusCode < 300) {
      return response;
    } else {
      throw new Error(
        `HTTP ${response?.statusCode || "unknown"}: ${
          response?.errMsg || "请求失败"
        }`
      );
    }
  } catch (error) {
    console.warn(
      `请求失败 (尝试 ${retryCount + 1}/${RETRY_CONFIG.maxRetries + 1}):`,
      error.message
    );

    // 如果还有重试次数且是网络相关错误
    if (retryCount < RETRY_CONFIG.maxRetries && isRetryableError(error)) {
      const delayTime =
        RETRY_CONFIG.retryDelay *
        Math.pow(RETRY_CONFIG.retryDelayMultiplier, retryCount);
      console.log(`${delayTime}ms 后重试...`);

      await delay(delayTime);
      return requestWithRetry(requestConfig, retryCount + 1);
    }

    // 重试次数用完或不可重试的错误，抛出原始错误
    throw error;
  }
}

// 判断是否为可重试的错误
function isRetryableError(error) {
  const retryableErrors = [
    "timeout",
    "network error",
    "connection failed",
    "网络连接不可用",
    "request:fail",
    "请求失败",
    "HTTP 500",
    "HTTP 502",
    "HTTP 503",
    "HTTP 504",
    "HTTP unknown",
  ];

  return retryableErrors.some(
    (errorType) =>
      error.message &&
      error.message.toLowerCase().includes(errorType.toLowerCase())
  );
}

const service = function (config) {
  const requestConfig = {
    timeout: API_TIMEOUT,
    ...config,
    url: BASE_URL + config.url,
  };

  return requestWithRetry(requestConfig);
};

const get = function (url, data) {
  const requestConfig = {
    url: BASE_URL + url,
    data,
    sslVerify: false,
    method: "GET",
    timeout: API_TIMEOUT,
    header: {
      token: getToken(),
      "content-type": "application/json",
    },
  };

  return requestWithRetry(requestConfig);
};

const post = function (url, data) {
  const requestConfig = {
    url: BASE_URL + url,
    data,
    sslVerify: false,
    method: "POST",
    timeout: API_TIMEOUT,
    header: {
      token: getToken(),
      "content-type": "application/json",
    },
  };

  return requestWithRetry(requestConfig);
};
const upFile = function (data) {
  const uploadConfig = {
    url: BASE_URL + "/information/addFile",
    formData: data,
    timeout: API_TIMEOUT,
  };

  // 文件上传也使用重试机制
  return requestWithRetry(uploadConfig);
};

// 错误处理工具函数
function handleRequestError(error, showToast = true) {
  let errorMessage = "请求失败，请稍后重试";

  if (error.message) {
    if (error.message.includes("timeout")) {
      errorMessage = "请求超时，请检查网络连接";
    } else if (error.message.includes("网络连接不可用")) {
      errorMessage = "网络连接不可用，请检查网络设置";
    } else if (error.message.includes("HTTP 4")) {
      errorMessage = "请求参数错误";
    } else if (error.message.includes("HTTP 5")) {
      errorMessage = "服务器错误，请稍后重试";
    }
  }

  if (showToast) {
    uni.showToast({
      title: errorMessage,
      icon: "error",
      duration: 2000,
    });
  }

  return errorMessage;
}

// 网络状态监听
function setupNetworkListener() {
  uni.onNetworkStatusChange((res) => {
    if (!res.isConnected) {
      uni.showToast({
        title: "网络连接已断开",
        icon: "error",
        duration: 2000,
      });
    } else {
      console.log("网络已连接:", res.networkType);
    }
  });
}

export {
  service,
  get,
  post,
  upFile,
  BASE_URL,
  handleRequestError,
  setupNetworkListener,
  checkNetworkStatus,
};
