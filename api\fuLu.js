import { post, get } from "@/utils/requset.js"

/**
 * 获取上架商品列表
 */
const getGoodsList = function() {
	return post("/FuLu/userGetGoodsList")
}

/**
 * 获取商品详细信息
 */
const userGetGoodsInfo = function(data) {
	return post('/FuLu/userGetGoodsInfo', data)
}

/**
 * 生成订单
 */
const creatOrder = function(data) {
	return post('/FuLu/creatOrder', data)
}

/**
 * 获取支付参数
 */
const flAppPayForPrepayid = function(data) {
	return get('/WxPay/flAppPayForPrepayid', data)
}

/**
 * 通过手机号查询权益商品列表
 */
const getAllUsOrders = function(data) {
	return post('/FuLu/getAllUsOrders', data)
}

export {
	getGoodsList,
	userGetGoodsInfo,
	creatOrder,
	flAppPayForPrepayid,
	getAllUsOrders
}