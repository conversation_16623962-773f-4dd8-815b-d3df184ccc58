﻿<template>
  <div class="app-container">
    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <i class="el-icon-goods" />
            <span>福禄权益商品</span>
          </div>
          <div class="header-actions">
            <el-button
              type="info"
              icon="el-icon-refresh"
              class="refresh-btn"
              @click="handleRefresh"
            >
              刷新
            </el-button>
          </div>
        </div>

        <el-table
          :data="goods"
          stripe
          style="width: 100%"
          class="modern-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#374151',
            fontWeight: '600',
          }"
          :row-style="{ height: '60px' }"
          v-loading="loading"
        >
          <el-table-column
            prop="projuctId"
            label="商品ID"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span class="product-id">#{{ scope.row.projuctId }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="details.product_name"
            label="商品名"
            min-width="180"
          >
            <template slot-scope="scope">
              <div class="product-name-cell">
                <i class="el-icon-goods product-icon" />
                <span>{{ scope.row.details.product_name }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="facePrice"
            label="面值"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span class="price-text">¥{{ scope.row.facePrice }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="platformUnitPrice"
            label="进货价"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span class="price-text purchase-price"
                >¥{{ scope.row.platformUnitPrice }}</span
              >
            </template>
          </el-table-column>

          <el-table-column
            prop="ownUnitPrice"
            label="定价"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span class="price-text selling-price"
                >¥{{ scope.row.ownUnitPrice }}</span
              >
            </template>
          </el-table-column>

          <el-table-column label="货品状态" width="120" align="center">
            <template slot-scope="scope">
              <el-tag
                :type="scope.row.status === '1' ? 'success' : 'info'"
                size="small"
                class="status-tag"
              >
                {{ scope.row.status === "1" ? "上架" : "下架" }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="small"
                :type="scope.row.status === '1' ? 'danger' : 'success'"
                :icon="
                  scope.row.status === '1' ? 'el-icon-bottom' : 'el-icon-top'
                "
                class="action-btn status-btn"
                @click="setGoodsStatus(scope.row)"
              >
                {{ scope.row.status === "1" ? "下架" : "上架" }}
              </el-button>
              <el-button
                size="small"
                type="primary"
                icon="el-icon-edit"
                class="action-btn edit-btn"
                @click="updateMoney(scope.row)"
              >
                修价
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 修改价格对话框 -->
    <el-dialog
      title="商品信息修改"
      :visible.sync="dialogVisible"
      width="500px"
      :before-close="
        () => {
          dialogVisible = false;
        }
      "
      class="goods-dialog"
    >
      <div class="dialog-content">
        <div class="goods-info">
          <div class="info-item">
            <label>商品名称</label>
            <el-input
              v-model="good.details.product_name"
              placeholder="请输入商品名称"
            />
          </div>

          <div class="info-row">
            <div class="info-item readonly">
              <label>进货价</label>
              <div class="readonly-value">¥{{ good.platformUnitPrice }}</div>
            </div>
            <div class="info-item readonly">
              <label>面值</label>
              <div class="readonly-value">¥{{ good.facePrice }}</div>
            </div>
          </div>

          <div class="info-item">
            <label>定价</label>
            <el-input
              v-model="good.ownUnitPrice"
              type="number"
              placeholder="请输入价格"
              prefix-icon="el-icon-money"
            />
          </div>
        </div>
      </div>

      <span slot="footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="setOwnPrice(good)">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  loadManyByPramas,
  getGoodsList,
  setGoodsStatus,
  setOwnPrice,
} from "@/api/flGoods";
export default {
  data() {
    return {
      goods: [],
      loading: false,
      dialogVisible: false,
      good: {
        details: {},
        ownUnitPrice: 0,
      },
    };
  },
  mounted() {
    this.initData();
  },
  methods: {
    // 刷新数据
    handleRefresh() {
      this.loadManyByPramas();
    },
    loadManyByPramas() {
      this.loading = true;
      loadManyByPramas()
        .then((res) => {
          const data = res.data;
          const goods = data.map((g, i) => {
            let details = {};
            try {
              details = JSON.parse(g.details);
            } catch {
              console.log(i);
              console.log(g.details);
            }
            return {
              ...g,
              details,
            };
          });
          this.goods = goods;
        })
        .catch((error) => {
          console.error("加载商品数据失败:", error);
          this.$message.error("加载商品数据失败");
        })
        .finally(() => {
          this.loading = false;
        });
    },
    initData() {
      getGoodsList().then((_) => {
        this.loadManyByPramas();
      });
    },
    setGoodsStatus(good) {
      const data = {
        projuct_id: good.projuctId,
      };
      setGoodsStatus(data).then((res) => {
        res = res.data;
        if (res.code !== 200) {
          this.$message.error("修改失败");
          return;
        }
        // 修改成功，不显示消息提示
        this.loadManyByPramas();
      });
    },
    // 点击改价
    updateMoney(good) {
      this.good = {
        ...good,
      };
      this.dialogVisible = true;
    },
    // 修改定价
    setOwnPrice(good) {
      const data = {
        projuct_id: good.projuctId,
        own_price: Number(good.ownUnitPrice),
        name: this.good.details.product_name,
      };
      if (Object.is(data.own_price, NaN)) {
        this.$message("价格请输入数字");
        return;
      }
      this.dialogVisible = false;
      setOwnPrice(data).then((res) => {
        console.log(res);
        res = res.data;
        if (res.code !== 200) {
          this.$message.error("修改失败");
          return;
        }
        // 修改成功，不显示消息提示
        this.loadManyByPramas();
      });
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 6px;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 8px;
}

.table-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
  padding: 8px 10px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.table-title i {
  margin-right: 6px;
  color: #409eff;
  font-size: 1.1rem;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.refresh-btn {
  background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
  border: none;
}

.refresh-btn:hover {
  box-shadow: 0 3px 12px rgba(144, 147, 153, 0.4);
}

.product-id {
  font-weight: 600;
  color: #409eff;
}

.product-name-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-icon {
  color: #10b981;
  font-size: 1rem;
}

.price-text {
  font-weight: 600;
  color: #059669;
}

.purchase-price {
  color: #dc2626;
}

.selling-price {
  color: #0d9488;
}

.status-tag {
  font-weight: 600;
}

.action-btn {
  transition: all 0.3s ease;
  margin-right: 6px;
}

.status-btn:hover {
  box-shadow: 0 3px 12px rgba(239, 68, 68, 0.4);
}

.edit-btn {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.edit-btn:hover {
  box-shadow: 0 3px 12px rgba(64, 158, 255, 0.4);
}

/* 对话框样式 */
.goods-dialog .dialog-content {
  padding: 8px 0;
}

.goods-info {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-item label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 0.8rem;
}

.info-item.readonly .readonly-value {
  padding: 12px 15px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #6b7280;
  font-weight: 500;
}

.cancel-btn {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  color: #6b7280;
}

.cancel-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.confirm-btn {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
  border-radius: 8px;
  margin-left: 12px;
}

.confirm-btn:hover {
  box-shadow: 0 3px 12px rgba(64, 158, 255, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 4px;
  }

  .info-row {
    grid-template-columns: 1fr;
    gap: 6px;
  }
}
</style>
