<template>
  <view class="binding-container">
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 车牌号输入卡片 -->
      <view class="info-card">
        <view class="card-title">请输入您的车牌号</view>
        <view class="card-desc">
          一辆车仅可申办一台ETC，暂仅支持蓝牌或绿牌，且9座及以下车辆办理
        </view>
        <car-number v-model="carNumber" :value="carNumber"></car-number>
      </view>

      <!-- 车主信息卡片 -->
      <view class="info-card">
        <view class="card-title">车主信息</view>

        <view class="input-group">
          <view class="input-label">车主姓名</view>
          <view class="input-wrapper">
            <view class="input-icon">
              <uni-icons
                type="person"
                size="16"
                color="#999"
                class="person-icon"
              ></uni-icons>
            </view>
            <input
              class="form-input"
              type="text"
              placeholder="请输入姓名"
              v-model="name"
              placeholder-class="input-placeholder"
            />
          </view>
        </view>

        <view class="input-group">
          <view class="input-label">车主手机号</view>
          <view class="input-wrapper">
            <view class="input-icon">
              <uni-icons
                type="phone"
                size="16"
                color="#999"
                class="phone-icon"
              ></uni-icons>
            </view>
            <input
              class="form-input"
              type="number"
              placeholder="请输入手机号"
              v-model="phone"
              placeholder-class="input-placeholder"
            />
          </view>
        </view>
      </view>

      <!-- 邮寄地址卡片 -->
      <view class="info-card">
        <view class="card-title">邮寄地址</view>
        <view class="textarea-wrapper">
          <view class="textarea-icon">
            <uni-icons
              type="location"
              size="16"
              color="#999"
              class="location-icon"
            ></uni-icons>
          </view>
          <textarea
            class="form-textarea"
            placeholder="请输入您的收货地址"
            v-model="address"
            placeholder-class="input-placeholder"
          ></textarea>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-action">
      <button class="submit-btn" @click="saveInfo">
        <text>下一步</text>
      </button>
    </view>
  </view>
</template>

<script>
import { mapGetters } from "vuex";
import CarNumber from "../../components/codecook-carnumber/codecook-carnumber.vue";
import {
  ifGivePage,
  updUser,
  showInformations,
  getPhoneNoInfo,
} from "@/api/user.js";

export default {
  components: {
    CarNumber,
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    return {
      carNumber: "",
      name: "",
      phone: "",
      address: "",
      goods: {},
    };
  },
  watch: {
    carNumber(num) {
      console.log(num);
    },
  },
  methods: {
    saveInfo() {
      // 验证信息，
      if (
        this.carNumber == "" ||
        this.carNumber == undefined ||
        this.carNumber == null ||
        this.carNumber.length < 7
      ) {
        uni.showToast({
          title: "请完整填写信息",
          icon: "error",
        });
        return;
      }
      if (this.name == "" || this.name == undefined || this.name == null) {
        uni.showToast({
          title: "请完整填写信息",
          icon: "error",
        });
        return;
      }
      if (this.phone == "" || this.phone == undefined || this.phone == null) {
        uni.showToast({
          title: "请完整填写信息",
          icon: "error",
        });
        return;
      }
      if (
        this.address == "" ||
        this.address == undefined ||
        this.address == null
      ) {
        uni.showToast({
          title: "请完整填写信息",
          icon: "error",
        });
        return;
      }
      console.log(this.userInfo);
      let data = {
        id: this.userInfo.id,
        carId: this.carNumber,
        phnumber: this.phone,
        realName: this.name,
        address: this.address,
      };
      updUser(data)
        .then((res) => {
          console.log("完整响应:", res);
          console.log("res.data:", res.data);
          console.log("typeof res.data:", typeof res.data);

          // 兼容不同的响应格式
          let responseData = res.data;
          if (responseData === "updSuccess" || responseData == "updSuccess") {
            uni.showToast({
              title: "添加成功",
              icon: "success",
            });
            let that = this;
            setTimeout(function () {
              console.log("准备跳转，商品信息:", that.goods);
              console.log("车牌号:", that.carNumber);
              uni.navigateTo({
                url:
                  "/subpackages/goods/pages/buy/buy?goods=" +
                  JSON.stringify(that.goods) +
                  "&carId=" +
                  JSON.stringify(that.carNumber),
              });
            }, 1000);
          } else {
            console.log("响应不是updSuccess，实际响应:", responseData);
            uni.showToast({
              title: "更新失败",
              icon: "error",
            });
          }
        })
        .catch((error) => {
          console.error("updUser接口调用失败:", error);
          uni.showToast({
            title: "网络请求失败",
            icon: "error",
          });
        });
    },
    // getPhoneNumber(e) {
    // 	let that = this;
    // 	//console.log("3 缓存的用户信息",  uni.getStorageSync("sessionUser"));
    // 	if (e.detail.errMsg == "getPhoneNumber:ok") { // 用户允许或去手机号
    // 		//获取到手机号码
    // 		let params = {
    // 			code: e.detail.code,
    // 			// encryptedData: e.detail.encryptedData,
    // 			// ivStr: e.detail.iv
    // 		};
    // 		console.log(params)
    // 		//调用后端接口getPhoneNoInfo方法
    // 		getPhoneNoInfo(params).then(res => {

    // 			if (res.data.code != 200) {
    // 				that.$msg.error(message)
    // 				return
    // 			}
    // 			//存入Storage
    // 			// uni.setStorageSync("sessionPhone",result.phoneNumber);
    // 			that.phone = res.data.data
    // 			console.log(that.phone)
    // 		})
    // 	}
    // },

    chuliData() {
      console.log(this.userInfo);
      this.carNumber = this.userInfo.carId;
      this.name = this.userInfo.realName;
      this.phone = this.userInfo.phnumber;
      this.address = this.userInfo.address;
      console.log(this.carNumber);
      if (
        this.userInfo.carId == null ||
        this.userInfo.carId == "" ||
        this.userInfo.carId == undefined ||
        this.userInfo.carId == "null"
      ) {
        this.carNumber = "";
      }
      if (
        this.userInfo.realName == null ||
        this.userInfo.realName == "" ||
        this.userInfo.realName == undefined ||
        this.userInfo.realName == "null"
      ) {
        this.name = "";
      }
      if (
        this.userInfo.phone == null ||
        this.userInfo.phone == "" ||
        this.userInfo.phone == undefined ||
        this.userInfo.phone == "null"
      ) {
        this.phone = "";
      }
      if (
        this.userInfo.address == null ||
        this.userInfo.address == "" ||
        this.userInfo.address == undefined ||
        this.userInfo.address == "null"
      ) {
        this.address = "";
      }
    },
  },
  beforeMount() {},
  onLoad(options) {
    console.log(options.goods);
    this.goods = JSON.parse(options.goods);
    console.log(this.goods);
  },
  onShow() {
    this.chuliData();
  },
};
</script>

<style lang="scss" scoped>
.binding-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 100px;
}

/* 主要内容 */
.main-content {
  padding: 20px;
}

/* 信息卡片 */
.info-card {
  background: #fff;
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.08);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  letter-spacing: 0.3px;
}

.card-desc {
  font-size: 12px;
  color: #999;
  line-height: 1.5;
  margin-bottom: 20px;
  padding: 12px;
  background: rgba(0, 212, 170, 0.05);
  border-radius: 10px;
  border-left: 3px solid #00d4aa;
}

/* 输入组 */
.input-group {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.input-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.input-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #00d4aa;
    background: #fff;
  }
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 12px 0 40px;
  font-size: 15px;
  border: none;
  background: transparent;
  color: #333;
  box-sizing: border-box;
}

.input-placeholder {
  color: #999;
}

/* 文本域 */
.textarea-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #00d4aa;
    background: #fff;
  }
}

.textarea-icon {
  position: absolute;
  left: 12px;
  top: 12px;
  z-index: 2;
}

.form-textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px 12px 12px 40px;
  font-size: 15px;
  border: none;
  background: transparent;
  color: #333;
  box-sizing: border-box;
  resize: none;
}

/* 底部操作 */
.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20px;
  box-shadow: 0 -2px 10px rgba(0, 212, 170, 0.1);
}

.submit-btn {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  color: #fff;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-sizing: border-box;
  letter-spacing: 1px;

  &:active {
    transform: scale(0.98);
  }

  text {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
