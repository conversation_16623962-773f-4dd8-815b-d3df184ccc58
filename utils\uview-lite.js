// 原生工具函数 - 替代 uView 工具函数
// 移除了对 uview 的依赖，使用原生实现

// 测试工具函数
const test = {
  // 是否为空
  isEmpty: (value) => {
    return value === undefined || value === null || value === "";
  },
  // 是否为数组
  array: (value) => {
    return Array.isArray(value);
  },
  // 是否为对象
  object: (value) => {
    return typeof value === "object" && value !== null && !Array.isArray(value);
  },
  // 是否为字符串
  string: (value) => {
    return typeof value === "string";
  },
  // 是否为数字
  number: (value) => {
    return typeof value === "number" && !isNaN(value);
  },
};

// 防抖函数
const debounce = (func, wait, immediate = false) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(this, args);
  };
};

// 节流函数
const throttle = (func, limit) => {
  let inThrottle;
  return function () {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// 时间格式化
const timeFormat = (timestamp, format = "yyyy-mm-dd") => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hour = String(date.getHours()).padStart(2, "0");
  const minute = String(date.getMinutes()).padStart(2, "0");
  const second = String(date.getSeconds()).padStart(2, "0");

  return format
    .replace("yyyy", year)
    .replace("mm", month)
    .replace("dd", day)
    .replace("hh", hour)
    .replace("MM", minute)
    .replace("ss", second);
};

// 时间距离现在
const timeFrom = (timestamp) => {
  const now = Date.now();
  const diff = now - timestamp;
  const minute = 60 * 1000;
  const hour = minute * 60;
  const day = hour * 24;
  const month = day * 30;
  const year = month * 12;

  if (diff < minute) return "刚刚";
  if (diff < hour) return Math.floor(diff / minute) + "分钟前";
  if (diff < day) return Math.floor(diff / hour) + "小时前";
  if (diff < month) return Math.floor(diff / day) + "天前";
  if (diff < year) return Math.floor(diff / month) + "个月前";
  return Math.floor(diff / year) + "年前";
};

// 添加单位
const addUnit = (value, unit = "px") => {
  if (test.isEmpty(value)) return "";
  if (test.number(value) || /^\d+(\.\d+)?$/.test(value)) {
    return value + unit;
  }
  return value;
};

// 添加样式
const addStyle = (customStyle) => {
  if (test.isEmpty(customStyle)) return {};
  if (test.object(customStyle)) return customStyle;
  if (test.string(customStyle)) {
    const styles = {};
    customStyle.split(";").forEach((style) => {
      const [key, value] = style.split(":");
      if (key && value) {
        styles[key.trim()] = value.trim();
      }
    });
    return styles;
  }
  return {};
};

// 深度合并对象
const deepMerge = (target, source) => {
  if (!test.object(target)) target = {};
  if (!test.object(source)) return target;

  const result = { ...target };
  Object.keys(source).forEach((key) => {
    if (test.object(source[key]) && test.object(target[key])) {
      result[key] = deepMerge(target[key], source[key]);
    } else {
      result[key] = source[key];
    }
  });
  return result;
};

// 创建精简版$u对象
const $u = {
  test,
  debounce,
  throttle,
  timeFormat,
  timeFrom,
  addUnit,
  addStyle,
  deepMerge,
  // 常用的类型数组
  type: ["primary", "success", "error", "warning", "info"],
};

export default $u;
