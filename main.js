import App from "./App";
import store from "./store";
import { setupNetworkListener } from "@/utils/requset.js";

// 初始化网络状态监听（仅微信小程序需要）
// #ifdef MP-WEIXIN
setupNetworkListener();
// #endif

// #ifndef VUE3
import Vue from "vue";
import $u from "@/utils/uview-lite.js";

Vue.prototype.$u = $u;
uni.$u = $u;
Vue.config.productionTip = false;
App.mpType = "app";
Vue.prototype.$store = store;

const app = new Vue({
  store,
  ...App,
});
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from "vue";

export function createApp() {
  const app = createSSRApp(App);
  return { app };
}
// #endif
