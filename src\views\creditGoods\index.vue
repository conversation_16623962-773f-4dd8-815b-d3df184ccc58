<template>
  <div class="app-container">
    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <i class="el-icon-phone" />
            <span>话费配置</span>
          </div>
          <div class="header-actions">
            <el-button
              type="info"
              icon="el-icon-refresh"
              @click="handleRefresh"
            >
              刷新
            </el-button>
          </div>
        </div>

        <el-table
          :data="creditGoodsList"
          stripe
          style="width: 100%"
          class="modern-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#374151',
            fontWeight: '600',
          }"
          :row-style="{ height: '60px' }"
          v-loading="loading"
        >
          <el-table-column prop="id" label="ID" width="80" align="center">
            <template slot-scope="scope">
              <span class="product-id">#{{ scope.row.id }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="cerditId"
            label="商品ID"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span class="credit-id">{{ scope.row.cerditId }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="title" label="商品名" min-width="180">
            <template slot-scope="scope">
              <div class="product-name-cell">
                <i class="el-icon-phone product-icon" />
                <span>{{ scope.row.title }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="image" label="图片" width="100" align="center">
            <template slot-scope="scope">
              <el-image
                v-if="scope.row.image"
                :src="scope.row.image"
                :preview-src-list="[scope.row.image]"
                class="product-image"
                fit="cover"
              />
              <span v-else class="no-image">暂无图片</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="facePrice"
            label="面值"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span class="price-text">¥{{ scope.row.facePrice }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="realPrice"
            label="平台价格"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span class="price-text platform-price"
                >¥{{ scope.row.realPrice }}</span
              >
            </template>
          </el-table-column>

          <el-table-column
            prop="userPay"
            label="用户支付价格"
            width="130"
            align="center"
          >
            <template slot-scope="scope">
              <span class="price-text user-price"
                >¥{{ scope.row.userPay }}</span
              >
            </template>
          </el-table-column>

          <el-table-column
            prop="integral"
            label="积分"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span class="integral-text">{{ scope.row.integral }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="discountRate"
            label="折扣"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span class="discount-text">{{ scope.row.discountRate }}%</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="primary"
                icon="el-icon-edit"
                class="action-btn edit-btn"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :before-close="handleDialogClose"
      class="credit-goods-dialog"
    >
      <el-form
        ref="creditGoodsForm"
        :model="currentItem"
        :rules="formRules"
        label-width="120px"
        class="dialog-form"
      >
        <el-form-item label="商品ID" prop="cerditId">
          <el-input v-model="currentItem.cerditId" placeholder="请输入商品ID" />
        </el-form-item>

        <el-form-item label="商品图片" prop="image">
          <el-upload
            class="image-uploader"
            action="#"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleImageChange"
            :disabled="imageUploading"
            accept="image/jpeg,image/jpg,image/png"
          >
            <img
              v-if="currentItem.image && !imageUploading"
              :src="currentItem.image"
              class="uploaded-image"
            />
            <div v-else-if="imageUploading" class="uploading-placeholder">
              <i class="el-icon-loading upload-icon" />
              <div class="upload-text">上传中...</div>
            </div>
            <div v-else class="upload-placeholder">
              <i class="el-icon-plus upload-icon" />
              <div class="upload-text">上传图片</div>
            </div>
          </el-upload>
          <div
            v-if="currentItem.image && !imageUploading"
            class="image-actions"
          >
            <el-button type="text" size="small" @click="previewImage">
              <i class="el-icon-zoom-in" /> 预览
            </el-button>
            <el-button type="text" size="small" @click="removeImage">
              <i class="el-icon-delete" /> 删除
            </el-button>
          </div>
          <div class="upload-tip">
            <i class="el-icon-info" />
            <span
              >支持 JPG、PNG 格式，建议尺寸 200x200 像素，文件大小不超过
              2MB</span
            >
          </div>
        </el-form-item>

        <el-form-item label="折扣" prop="discountRate">
          <el-input
            v-model.number="currentItem.discountRate"
            type="number"
            placeholder="请输入折扣百分比"
            suffix-text="%"
          />
        </el-form-item>
      </el-form>

      <span slot="footer">
        <el-button @click="handleDialogClose"> 取消 </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </span>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog
      title="图片预览"
      :visible.sync="previewDialogVisible"
      width="500px"
      class="image-preview-dialog"
    >
      <div class="preview-container">
        <img :src="currentItem.image" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCreditGoodsList, updateCreditGoods } from "@/api/creditGoods";
import { uploadImage } from "@/api/selfOperated";
export default {
  name: "CreditGoods",
  data() {
    return {
      creditGoodsList: [],
      loading: false,
      dialogVisible: false,
      dialogTitle: "编辑话费配置",
      submitting: false,
      imageUploading: false, // 图片上传状态
      previewDialogVisible: false, // 图片预览对话框
      currentItem: {
        id: null,
        cerditId: "",
        discountRate: 0,
        image: "", // 图片URL
      },
      formRules: {
        cerditId: [
          { required: true, message: "请输入商品ID", trigger: "blur" },
        ],
        discountRate: [
          { required: true, message: "请输入折扣", trigger: "blur" },
          { type: "number", message: "折扣必须为数字", trigger: "blur" },
        ],
      },
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        const response = await getCreditGoodsList();
        if (response.data && response.data.code === 200) {
          this.creditGoodsList = response.data.data || [];
        } else {
          this.$message.error("获取数据失败");
        }
      } catch (error) {
        console.error("加载数据失败:", error);
        this.$message.error("获取数据失败");
      } finally {
        this.loading = false;
      }
    },

    // 刷新数据
    handleRefresh() {
      this.loadData();
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = "编辑话费配置";
      this.currentItem = { ...row };
      this.dialogVisible = true;
    },

    // 提交表单
    handleSubmit() {
      this.$refs.creditGoodsForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          try {
            const response = await updateCreditGoods(this.currentItem);

            if (response.data && response.data.code === 200) {
              // 更新成功，不显示消息提示
              this.dialogVisible = false;
              this.loadData();
            } else {
              this.$message.error("更新失败");
            }
          } catch (error) {
            console.error("提交失败:", error);
            this.$message.error("更新失败");
          } finally {
            this.submitting = false;
          }
        }
      });
    },

    // 关闭对话框
    handleDialogClose() {
      this.dialogVisible = false;
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.currentItem = {
        id: null,
        cerditId: "",
        discountRate: 0,
        image: "",
      };
      if (this.$refs.creditGoodsForm) {
        this.$refs.creditGoodsForm.clearValidate();
      }
    },

    // 图片上传变化处理
    handleImageChange(file, fileList) {
      if (file && file.raw) {
        // 验证文件类型
        const isJPGOrPNG =
          file.raw.type === "image/jpeg" ||
          file.raw.type === "image/jpg" ||
          file.raw.type === "image/png";
        if (!isJPGOrPNG) {
          this.$message.error("只能上传 JPG/PNG 格式的图片!");
          return;
        }

        // 验证文件大小
        const isLt2M = file.raw.size / 1024 / 1024 < 2;
        if (!isLt2M) {
          this.$message.error("图片大小不能超过 2MB!");
          return;
        }

        // 创建FormData对象上传图片
        const formData = new FormData();
        formData.append("file", file.raw);

        // 显示上传中状态
        this.imageUploading = true;

        // 调用上传图片接口
        uploadImage(formData)
          .then((result) => {
            if (result && result.data && result.data.code === 200) {
              // 只有上传成功后才更新图片URL
              this.currentItem.image = result.data.data;
              this.$message.success("图片上传成功");
            } else {
              this.$message.error(result.data.message || "图片上传失败");
              // 上传失败，保持原来的图片
            }
          })
          .catch((err) => {
            console.error("图片上传出错:", err);
            this.$message.error("图片上传失败");
            // 上传失败，保持原来的图片
          })
          .finally(() => {
            this.imageUploading = false;
          });
      }
    },

    // 删除图片
    removeImage() {
      this.$confirm("确定删除这张图片吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.currentItem.image = "";
          this.$message.success("图片已删除");
        })
        .catch(() => {
          // 取消删除
        });
    },

    // 预览图片
    previewImage() {
      this.previewDialogVisible = true;
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 6px;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 8px;
}

.table-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
  padding: 8px 10px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.table-title i {
  margin-right: 6px;
  color: #409eff;
  font-size: 1.1rem;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.table-actions {
  display: flex;
  gap: 8px;
}

.add-btn {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border: none;

  transition: all 0.3s ease;
}

.add-btn:hover {
  box-shadow: 0 3px 12px rgba(103, 194, 58, 0.4);
}

.product-id {
  font-weight: 600;
  color: #409eff;
}

.credit-id {
  font-weight: 600;
  color: #606266;
}

.product-name-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-icon {
  color: #10b981;
  font-size: 1rem;
}

.product-image {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  cursor: pointer;
}

.no-image {
  color: #909399;
  font-size: 12px;
}

.price-text {
  font-weight: 600;
  color: #059669;
}

.platform-price {
  color: #dc2626;
}

.user-price {
  color: #0d9488;
}

.integral-text {
  font-weight: 600;
  color: #f59e0b;
}

.discount-text {
  font-weight: 600;
  color: #8b5cf6;
}

.action-btn {
  transition: all 0.3s ease;
  margin-right: 6px;
}

.edit-btn {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
}

.edit-btn:hover {
  box-shadow: 0 3px 12px rgba(64, 158, 255, 0.4);
}

.delete-btn:hover {
  box-shadow: 0 3px 12px rgba(245, 101, 101, 0.4);
}

.disabled-btn {
  opacity: 0.5;
  cursor: not-allowed;
}

.disabled-btn:hover {
  box-shadow: none !important;
  opacity: 0.5;
}

/* 对话框样式 */
.credit-goods-dialog .dialog-form {
  padding: 8px 0;
}

/* 图片上传样式 */
.image-uploader {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    width: 148px;
    height: 148px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-upload:hover {
    border-color: #409eff;
  }

  .uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .upload-placeholder,
  .uploading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;
  }

  .uploading-placeholder {
    color: #409eff;
  }

  .upload-icon {
    font-size: 28px;
    margin-bottom: 8px;
  }

  .upload-text {
    font-size: 14px;
  }
}

.image-actions {
  margin-top: 8px;
  text-align: center;

  .el-button + .el-button {
    margin-left: 10px;
  }

  .el-button--text {
    color: #409eff;
    padding: 0;
    font-size: 12px;

    &:hover {
      color: #66b1ff;
    }

    i {
      margin-right: 2px;
    }
  }
}

.upload-tip {
  display: flex;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: #6b7280;

  i {
    margin-right: 4px;
    color: #93c5fd;
  }
}

/* 图片预览对话框样式 */
.image-preview-dialog {
  .preview-container {
    text-align: center;
    padding: 20px;
  }

  .preview-image {
    max-width: 100%;
    max-height: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.cancel-btn {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  color: #6b7280;
}

.cancel-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.confirm-btn {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
  border-radius: 8px;
  margin-left: 12px;
}

.confirm-btn:hover {
  box-shadow: 0 3px 12px rgba(64, 158, 255, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 4px;
  }

  .table-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }
}
</style>
