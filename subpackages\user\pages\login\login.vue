<template>
  <view class="bkcolor">
    <view class="wd" style="margin-top: 200px; transform: translateY(-50%)">
      <!-- <form>
				<view class="login-form-input">
					<input maxlength="11" placeholder="请输入手机号" v-model="loginForm.phone" />
					<view class="login-line"></view>
					<view class="login-verification">
						<input maxlength="6" placeholder="验证码" v-model="loginForm.verificationCode"
							style="width: 70%;" />
						<view v-if="verification === false" style="font-size: 14px;" class="verification-buttom"
							@click="sendVerification">获取验证码</view>
						<view style="font-size: 14px;" v-else>还有{{verificationSecond}}s可重发</view>
					</view>
				</view>
				<button form-type="submit"
					style="margin-top: 20px; border-radius: 20px; font-size: 15px; background: #4089FB; color: white;"
					@click="checkLogin">登录</button>
			</form> -->
      <!-- <view style="text-align: center; margin-top: 20px; font-size: 14px;">
				<view :class="agreement ? 'backcolorIsBlue' : ''"
					style="margin-right: 5px; border: 1px solid #7f7f7f; border-radius: 15px; width: 18px; height: 18px; display: inline-block; line-height: 18px;">
					<uni-icons type="checkmarkempty" :color="agreement ? 'white' : '#F4F4F4'" size="15"
						@click="agreement = (!agreement)"></uni-icons>
				</view>我已阅读并协议<text @click="openPrivacyPolicy">隐私协议</text>和<text @click="openUserProtocol">用户协议</text>
			</view> -->
    </view>
  </view>
</template>

<script>
import { sendCode, checkLogin } from "@/api/login.js";
import { validPhone, validCode } from "@/utils/valid.js";
import {
  resetInfo,
  setToken,
  setUserId,
  setUserToken,
  resetUserInfo,
  clearProxyLoginStatus,
} from "@/utils/auth.js";
import { mapActions } from "vuex";
let timer;
export default {
  data() {
    return {
      verification: false,
      verificationSecond: 0,
      loginForm: {
        phone: "",
        verificationCode: "",
        adminRabk: "0",
      },
      isMessage: false,
      // 协议按钮
      agreement: false,
      invitationCode: null,
    };
  },
  methods: {
    // 发送手机验证码
    sendVerification() {
      console.log("发起验证码");
      if (this.verification === true || this.isMessage === true) {
        return;
      }

      let data = {
        phone: this.loginForm.phone,
      };

      if (!validPhone(data.phone)) {
        return;
      }
      data.invitationCode = this.invitationCode;

      this.isMessage = true;
      sendCode(data)
        .then((res) => {
          console.log(res);
        })
        .finally(() => {
          this.verification = true;
          this.isMessage = false;
          this.verificationSecond = 59;
          clearInterval(timer);
          timer = setInterval(() => {
            this.verificationSecond = this.verificationSecond - 1;
            if (this.verificationSecond <= 0) {
              clearInterval(timer);
              this.verification = false;
            }
          }, 1000);
        });
    },

    // 登录
    checkLogin() {
      if (!this.agreement) {
        uni.showToast({
          title: "请勾选协议",
          icon: "none",
        });
        return;
      }
      if (this.isMessage) {
        return;
      }

      // 验证表单
      const data = {
        ...this.loginForm,
      };
      if (!validPhone(data.phone)) {
        uni.showToast({
          icon: "error",
          title: "手机号格式有误",
        });
        return;
      }
      if (!validCode(data.verificationCode)) {
        uni.showToast({
          icon: "error",
          title: "验证码格式有误",
        });
        return;
      }

      this.isMessage = true;
      checkLogin(data)
        .then((res) => {
          if (res.data.code !== 200) {
            uni.showToast({
              icon: "error",
              title: "登录失败",
            });
            return;
          }
          const token = res.data.data.token;
          const userId = res.data.data.userId;
          console.log(token);

          // 清除之前的登录信息（邀请码会被自动保留）
          resetInfo();

          // 设置用户端登录信息（使用独立的用户端token存储）
          setUserToken(token);
          setUserId(userId);
          clearProxyLoginStatus(); // 确保清除代理端标识
          this["user/getUserInfo"]();
          uni.reLaunch({
            url: "/pages/my/my",
          });
        })
        .finally(() => {
          this.isMessage = false;
        });
    },
    ...mapActions(["user/getUserInfo"]),

    // toLogin() {
    // 	if (this.agreement) {
    // 		uni.showToast({
    // 			title: "登录中",
    // 			icon: "loading"
    // 		})

    // 		let that = this
    // 		uni.login({
    // 			"provider": "weixin",
    // 			"onlyAuthorize": true, // 微信登录仅请求授权认证
    // 			success: function(event) {
    // 				const {
    // 					code
    // 				} = event
    // 				console.log(event.code)
    // 				//客户端成功获取授权临时票据（code）,向业务服务器发起登录请求。
    // 				let data = {
    // 					code: event.code
    // 				}
    // 				wxlogin(data).then(res => {
    // 					console.log("登录返回信息")
    // 					console.log(res)
    // 				})
    // 			},
    // 			fail: function(err) {
    // 				console.log(err)
    // 				// 登录授权失败
    // 				// err.code是错误码
    // 			}
    // 		})
    // 	} else {
    // 		uni.showToast({
    // 			title: '请阅读同意《服务协议》和《隐私政策协议》',
    // 			icon: 'none',
    // 			duration: 2000
    // 		});
    // 	}
    // },
  },
  onUnload() {
    clearInterval(timer);
    uni.hideLoading();
  },
  watch: {
    isMessage(c) {
      if (c) {
        uni.showLoading({
          mask: true,
          title: "加载中",
        });
      } else {
        uni.hideLoading();
      }
    },
  },
  onLoad(config) {
    console.log(config);
    if (config.myInvitationCode !== undefined) {
      this.invitationCode = config.myInvitationCode;
    }
  },
};
</script>

<style>
.login-form-input {
  background: white;
  overflow: hidden;
  border-radius: 5px;
  padding: 0 10px;
}

.login-form-input input {
  margin: 10px 0;
  display: block;
  font-size: 14px;
}

.login-line {
  height: 3px;
  background-color: #f4f4f4;
}

.login-verification {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.verification-buttom {
  color: #4089fb;
}

.backcolorIsBlue {
  background-color: #4089fb;
}
</style>
