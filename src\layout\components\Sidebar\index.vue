<template>
  <div
    class="relative h-full overflow-hidden transition-all duration-300 ease-out"
  >
    <div
      class="relative h-full w-full bg-gradient-to-br from-white/95 via-gray-50/92 via-25% via-slate-100/88 via-50% via-slate-200/85 via-75% to-slate-300/82 backdrop-blur-[20px] backdrop-saturate-[180%] rounded-r-3xl shadow-[0_8px_32px_rgba(0,0,0,0.08),0_2px_16px_rgba(0,0,0,0.04),inset_1px_0_0_rgba(255,255,255,0.3),inset_0_1px_0_rgba(255,255,255,0.2)] border-r border-white/20"
    >
      <!-- Logo区域 -->
      <logo v-if="showLogo" :collapse="isCollapse" />

      <!-- 导航菜单区域 -->
      <div class="overflow-y-auto overflow-x-hidden h-[calc(100%-40px)]">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapse"
          background-color="transparent"
          text-color="#374151"
          :unique-opened="false"
          active-text-color="#667eea"
          :collapse-transition="false"
          mode="vertical"
          :popper-append-to-body="true"
          class="modern-el-menu border-0 bg-transparent"
        >
          <sidebar-item
            v-for="route in routes"
            :key="route.path"
            :item="route"
            :base-path="route.path"
          />
        </el-menu>
      </div>

      <!-- 底部装饰区域 -->
      <div
        class="absolute bottom-0 left-0 right-0 border-t border-white/20"
        :class="isCollapse ? 'h-12 p-2' : 'h-15 p-4'"
      >
        <div
          class="w-full h-full bg-gradient-to-t from-gray-50/80 to-white/10 backdrop-blur-xl rounded-b-3xl"
          :class="isCollapse ? 'rounded-b-xl' : 'rounded-b-3xl'"
        >
          <div
            v-if="!isCollapse"
            class="flex items-center justify-between h-full px-4"
          >
            <div class="flex items-center">
              <div
                class="w-2 h-2 rounded-full bg-gradient-to-r from-green-500 to-green-600 mr-2 animate-pulse shadow-[0_0_0_4px_rgba(16,185,129,0.2)]"
              ></div>
              <span class="text-xs text-gray-500 font-medium"
                >系统运行正常</span
              >
            </div>
            <div
              class="text-[11px] text-gray-400 font-semibold bg-gradient-to-r from-indigo-500 to-purple-600 bg-clip-text text-transparent"
            >
              v{{ version }}
            </div>
          </div>
          <!-- 收起状态的简化状态显示 -->
          <div v-else class="flex items-center justify-center h-full">
            <div
              class="w-2 h-2 rounded-full bg-gradient-to-r from-green-500 to-green-600 animate-pulse shadow-[0_0_0_4px_rgba(16,185,129,0.2)]"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 侧边栏边框光效 -->
    <div
      class="absolute top-0 -right-0.5 w-0.5 h-full bg-gradient-to-b from-indigo-500/60 via-green-500/40 via-50% to-red-500/30 opacity-60 animate-pulse"
    ></div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";

export default {
  name: "ModernSidebar",
  components: {
    SidebarItem,
    Logo,
  },
  data() {
    return {
      version: "2.1.0",
    };
  },
  computed: {
    ...mapGetters(["sidebar"]),
    routes() {
      return this.$router.options.routes.filter((route) => !route.hidden);
    },
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo;
    },
    isCollapse() {
      return !this.sidebar.opened;
    },
  },
};
</script>

<style>
/* Element UI 菜单样式重写 */
.modern-el-menu .el-menu-item {
  background: transparent !important;
  color: #374151 !important;
  display: flex !important;
  align-items: center !important;
  line-height: 1.5 !important;
  margin: 4px 0 !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  min-height: 48px !important;
}

.modern-el-menu .el-menu-item.is-active {
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.12) 0%,
    rgba(16, 185, 129, 0.08) 100%
  ) !important;
  color: #667eea !important;
  transform: translateX(4px) !important;
  font-weight: 600 !important;
}

.modern-el-menu .el-menu-item.is-active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 2px 2px 0;
}

.modern-el-menu .el-submenu .el-submenu__title {
  background: transparent !important;
  color: #374151 !important;
  display: flex !important;
  align-items: center !important;
  border-radius: 12px !important;
  padding: 12px 16px !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
  min-height: 48px !important;
}
</style>
