// 错误处理和调试工具
import { isProduction } from "@/utils/config.js";

// 全局错误处理器
class ErrorHandler {
  constructor() {
    this.errorQueue = [];
    this.maxErrors = isProduction ? 10 : 50;
    this.reportUrl = "/api/error/report";
    this.enabled = isProduction;

    this.setupGlobalErrorHandler();
  }

  // 设置全局错误处理
  setupGlobalErrorHandler() {
    // 捕获未处理的Promise错误
    if (typeof window !== "undefined") {
      window.addEventListener("unhandledrejection", (event) => {
        this.handleError({
          type: "unhandledrejection",
          message: event.reason?.message || "Unhandled Promise Rejection",
          stack: event.reason?.stack,
          timestamp: new Date().toISOString(),
        });
      });

      // 捕获全局JavaScript错误
      window.addEventListener("error", (event) => {
        this.handleError({
          type: "javascript",
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack,
          timestamp: new Date().toISOString(),
        });
      });
    }
  }

  // 处理错误
  handleError(errorInfo) {
    // 在开发环境中直接输出到控制台
    if (!isProduction) {
      console.error("错误捕获:", errorInfo);
    }

    // 添加到错误队列
    this.errorQueue.push(errorInfo);

    // 保持队列大小
    if (this.errorQueue.length > this.maxErrors) {
      this.errorQueue.shift();
    }

    // 如果启用了错误上报，则上报错误
    if (this.enabled && isProduction) {
      this.reportError(errorInfo);
    }
  }

  // 上报错误到服务器
  async reportError(errorInfo) {
    try {
      await uni.request({
        url: this.reportUrl,
        method: "POST",
        data: {
          ...errorInfo,
          userAgent: navigator?.userAgent || "Unknown",
          url: window?.location?.href || "Unknown",
          platform: this.getPlatformInfo(),
        },
        timeout: 5000,
      });
    } catch (reportError) {
      console.warn("错误上报失败:", reportError);
    }
  }

  // 获取平台信息
  getPlatformInfo() {
    try {
      // #ifdef MP-WEIXIN
      const deviceInfo = wx.getDeviceInfo();
      return deviceInfo.platform || "Unknown";
      // #endif

      // #ifndef MP-WEIXIN
      return uni.getSystemInfoSync()?.platform || "Unknown";
      // #endif
    } catch (error) {
      return "Unknown";
    }
  }

  // 手动记录错误
  logError(error, context = {}) {
    const errorInfo = {
      type: "manual",
      message: error.message || error,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
    };

    this.handleError(errorInfo);
  }

  // 获取错误历史
  getErrorHistory() {
    return [...this.errorQueue];
  }

  // 清除错误历史
  clearErrorHistory() {
    this.errorQueue = [];
  }
}

// 创建全局错误处理器实例
const errorHandler = new ErrorHandler();

// 网络错误处理
export function handleNetworkError(error, context = {}) {
  const networkError = {
    type: "network",
    message: error.message || "网络请求失败",
    url: context.url || "Unknown",
    method: context.method || "Unknown",
    statusCode: context.statusCode,
    timestamp: new Date().toISOString(),
  };

  errorHandler.handleError(networkError);

  // 显示用户友好的错误提示
  let userMessage = "网络连接异常，请稍后重试";

  if (error.message?.includes("timeout")) {
    userMessage = "请求超时，请检查网络连接";
  } else if (error.message?.includes("404")) {
    userMessage = "请求的资源不存在";
  } else if (error.message?.includes("500")) {
    userMessage = "服务器内部错误";
  }

  uni.showToast({
    title: userMessage,
    icon: "error",
    duration: 2000,
  });

  return userMessage;
}

// 微信相关错误处理
export function handleWeixinError(error, context = {}) {
  const weixinError = {
    type: "weixin",
    message: error.message || "微信功能调用失败",
    context,
    timestamp: new Date().toISOString(),
  };

  errorHandler.handleError(weixinError);

  let userMessage = "微信功能暂时不可用";

  if (error.message?.includes("WeixinJSBridge")) {
    userMessage = "请在微信中打开此页面";
  } else if (error.message?.includes("permission")) {
    userMessage = "缺少必要的权限";
  }

  uni.showToast({
    title: userMessage,
    icon: "error",
    duration: 2000,
  });

  return userMessage;
}

// 调试工具
export const debugUtils = {
  // 打印网络请求信息
  logRequest(config, response) {
    if (!isProduction) {
      console.group(
        `🌐 网络请求: ${config.method?.toUpperCase()} ${config.url}`
      );
      console.log("请求配置:", config);
      console.log("响应数据:", response);
      console.groupEnd();
    }
  },

  // 打印性能信息
  logPerformance(label, startTime) {
    if (!isProduction) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      console.log(`⏱️ 性能: ${label} 耗时 ${duration}ms`);
    }
  },

  // 打印用户行为
  logUserAction(action, data = {}) {
    if (!isProduction) {
      console.log(`👤 用户行为: ${action}`, data);
    }
  },
};

export default errorHandler;
