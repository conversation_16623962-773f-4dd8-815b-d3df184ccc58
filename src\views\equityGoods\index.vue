<template>
  <div class="app-container">
    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <i class="el-icon-present" />
            <span>权益配置管理</span>
          </div>
          <div class="header-actions">
            <el-button
              type="info"
              icon="el-icon-refresh"
              @click="handleRefresh"
            >
              刷新
            </el-button>
            <el-button
              type="primary"
              icon="el-icon-plus"
              @click="handleAdd"
            >
              新增权益配置
            </el-button>
          </div>
        </div>

        <el-table
          :data="equityGoodsList"
          stripe
          style="width: 100%"
          class="modern-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#374151',
            fontWeight: '600',
          }"
          :row-style="{ height: '60px' }"
          v-loading="loading"
        >
          <el-table-column label="序号" width="80" align="center" type="index">
          </el-table-column>

          <el-table-column
            prop="name"
            label="权益卡名称"
            min-width="200"
            align="center"
          >
            <template slot-scope="scope">
              <div class="equity-name">
                <i class="el-icon-present equity-icon" />
                <span>{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="msg"
            label="权益卡介绍"
            min-width="250"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span class="equity-msg">{{ scope.row.msg }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="money" label="金额" width="120" align="center">
            <template slot-scope="scope">
              <span class="price-text money-text">¥{{ scope.row.money }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="integral"
            label="积分"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span class="integral-text">{{ scope.row.integral }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="primary"
                icon="el-icon-edit"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 添加/编辑权益配置对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :before-close="handleDialogClose"
      class="equity-goods-dialog"
    >
      <el-form
        ref="equityGoodsForm"
        :model="currentItem"
        :rules="formRules"
        label-width="120px"
        class="dialog-form"
      >
        <el-form-item label="权益卡名称" prop="name">
          <el-input
            v-model="currentItem.name"
            placeholder="请输入权益卡名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="权益卡介绍" prop="msg">
          <el-input
            v-model="currentItem.msg"
            type="textarea"
            placeholder="请输入权益卡介绍"
            :rows="4"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="金额" prop="money">
          <el-input-number
            v-model="currentItem.money"
            :precision="2"
            :step="0.01"
            :min="0"
            placeholder="请输入金额"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="积分" prop="integral">
          <el-input-number
            v-model="currentItem.integral"
            :precision="2"
            :step="0.01"
            :min="0"
            placeholder="请输入积分"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <span slot="footer">
        <el-button @click="handleDialogClose"> 取消 </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          确定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getEquityGoodsList,
  getEquityGoodsDetail,
  addEquityGoods,
  updateEquityGoods,
  deleteEquityGoods,
} from "@/api/equityGoods";

export default {
  name: "EquityGoods",
  data() {
    return {
      equityGoodsList: [], // 权益配置列表数据
      loading: false,
      dialogVisible: false,
      dialogTitle: "新增权益配置",
      submitting: false,
      currentItem: {
        id: null,
        name: "",
        msg: "",
        money: 0,
        integral: 0,
      },
      formRules: {
        name: [
          { required: true, message: "请输入权益卡名称", trigger: "blur" },
          {
            min: 1,
            max: 50,
            message: "长度在 1 到 50 个字符",
            trigger: "blur",
          },
        ],
        msg: [
          { required: true, message: "请输入权益卡介绍", trigger: "blur" },
          {
            min: 1,
            max: 200,
            message: "长度在 1 到 200 个字符",
            trigger: "blur",
          },
        ],
        money: [
          { required: true, message: "请输入金额", trigger: "blur" },
          {
            type: "number",
            min: 0,
            message: "金额必须大于等于0",
            trigger: "blur",
          },
        ],
        integral: [
          {
            type: "number",
            min: 0,
            message: "积分必须大于等于0",
            trigger: "blur",
          },
        ],
      },
    };
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 加载数据
    loadData() {
      this.loading = true;

      getEquityGoodsList()
        .then((res) => {
          this.loading = false;
          if (res && res.data && res.data.code === 200) {
            this.equityGoodsList = res.data.data || [];
          } else {
            this.$message.error(res.data.message || "获取数据失败");
            this.equityGoodsList = [];
          }
        })
        .catch((err) => {
          this.loading = false;
          console.error("获取权益配置列表出错:", err);
          this.$message.error("获取数据失败");
          this.equityGoodsList = [];
        });
    },

    // 刷新数据
    handleRefresh() {
      this.loadData();
    },

    // 新增权益配置
    handleAdd() {
      this.dialogTitle = "新增权益配置";
      this.currentItem = {
        id: null,
        name: "",
        msg: "",
        money: 0,
        integral: 0,
      };
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.equityGoodsForm.clearValidate();
      });
    },

    // 编辑权益配置
    handleEdit(row) {
      this.dialogTitle = "编辑权益配置";

      // 如果需要获取详细信息，调用详情接口
      if (row.id) {
        getEquityGoodsDetail(row.id)
          .then((res) => {
            if (res && res.data && res.data.code === 200) {
              this.currentItem = { ...res.data.data };
            } else {
              // 如果接口失败，使用列表中的数据
              this.currentItem = { ...row };
            }
            this.dialogVisible = true;
            this.$nextTick(() => {
              this.$refs.equityGoodsForm.clearValidate();
            });
          })
          .catch((err) => {
            console.error("获取权益详情失败:", err);
            // 如果接口失败，使用列表中的数据
            this.currentItem = { ...row };
            this.dialogVisible = true;
            this.$nextTick(() => {
              this.$refs.equityGoodsForm.clearValidate();
            });
          });
      } else {
        this.currentItem = { ...row };
        this.dialogVisible = true;
        this.$nextTick(() => {
          this.$refs.equityGoodsForm.clearValidate();
        });
      }
    },

    // 删除权益配置
    handleDelete(row) {
      this.$confirm("确认删除该权益配置吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteEquityGoods(row.id)
            .then((res) => {
              if (res && res.data && res.data.code === 200) {
                // 删除成功，不显示消息提示
                this.loadData();
              } else {
                this.$message.error(res.data.message || "删除失败");
              }
            })
            .catch((err) => {
              console.error("删除权益配置出错:", err);
              this.$message.error("删除失败");
            });
        })
        .catch(() => {
          // 取消删除，不显示消息提示
        });
    },

    // 关闭对话框
    handleDialogClose() {
      this.dialogVisible = false;
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.currentItem = {
        id: null,
        name: "",
        msg: "",
        money: 0,
        integral: 0,
      };
      if (this.$refs.equityGoodsForm) {
        this.$refs.equityGoodsForm.clearValidate();
      }
    },

    // 提交表单
    handleSubmit() {
      this.$refs.equityGoodsForm.validate((valid) => {
        if (valid) {
          this.submitting = true;
          const isEdit = !!this.currentItem.id;
          const apiCall = isEdit ? updateEquityGoods : addEquityGoods;
          const params = { ...this.currentItem };

          apiCall(params)
            .then((res) => {
              this.submitting = false;
              if (res && res.data && res.data.code === 200) {
                // 操作成功，不显示消息提示
                this.dialogVisible = false;
                this.loadData();
              } else {
                this.$message.error(
                  res.data.message || (isEdit ? "更新失败" : "添加失败")
                );
              }
            })
            .catch((err) => {
              this.submitting = false;
              console.error("提交权益配置出错:", err);
              this.$message.error(isEdit ? "更新失败" : "添加失败");
            });
        }
      });
    },
  },
};
</script>

<style scoped>
/* 容器样式 */
.app-container {
  padding: 6px;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 8px;
}

.table-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
  padding: 8px 10px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.table-title i {
  margin-right: 6px;
  color: #409eff;
  font-size: 1.1rem;
}

.header-actions {
  display: flex;
  gap: 12px;
}




/* 表格内容样式 */
.equity-name {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
}

.equity-icon {
  color: #10b981;
  font-size: 1rem;
}

.equity-msg {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.4;
}

.price-text {
  font-weight: 600;
  font-size: 0.9rem;
}

.money-text {
  color: #10b981;
}

.integral-text {
  color: #f59e0b;
  font-weight: 500;
}




/* 对话框样式 */
.dialog-form {
  padding: 8px 0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.form-item {
  display: flex;
  flex-direction: column;
}

.form-item label {
  margin-bottom: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.form-item .el-input,
.form-item .el-input-number {
  width: 100%;
}

.form-item .el-input :deep(.el-input__inner),
.form-item .el-input-number :deep(.el-input__inner) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.form-item .el-input :deep(.el-input__inner):focus,
.form-item .el-input-number :deep(.el-input__inner):focus {
  border-color: #409eff;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
}

.form-item .el-textarea :deep(.el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.form-item .el-textarea :deep(.el-textarea__inner):focus {
  border-color: #409eff;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
}

.dialog-footer {
  text-align: right;
  padding-top: 20px;
}

.cancel-btn {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  color: #6b7280;
}

.cancel-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.confirm-btn {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  border: none;
  border-radius: 8px;
  margin-left: 12px;
}

.confirm-btn:hover {
  box-shadow: 0 3px 12px rgba(64, 158, 255, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 4px;
  }

  .table-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }
}
</style>

.delete-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
}

.delete-btn:hover {
  box-shadow: 0 3px 12px rgba(239, 68, 68, 0.4);
}

/* 对话框样式 */
.equity-goods-dialog .el-dialog__header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e5e7eb;
}

.equity-goods-dialog .el-dialog__title {
  color: #1f2937;
  font-weight: 600;
}

.dialog-form {
  padding: 20px 0;
}

.dialog-form .el-form-item__label {
  font-weight: 500;
  color: #374151;
}

.dialog-form .el-input__inner,
.dialog-form .el-textarea__inner {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.dialog-form .el-input__inner:focus,
.dialog-form .el-textarea__inner:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.dialog-form .el-input-number {
  width: 100%;
}

.dialog-footer {
  padding: 12px 15px;
  color: #6b7280;
  font-weight: 500;
}

.cancel-btn {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  color: #6b7280;
}

.cancel-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.confirm-btn {
  border-radius: 8px;
  margin-left: 12px;
}

.confirm-btn:hover {
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 12px;
  }

  .table-card {
    padding: 16px;
  }

  .table-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }
}
</style>
