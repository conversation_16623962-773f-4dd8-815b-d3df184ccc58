<template>
  <view class="order-container">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <view class="nav-title">我的订单</view>
      <view class="nav-subtitle">查看您的所有订单信息</view>
    </view>

    <!-- 导航标签栏 -->
    <view class="tab-navigation">
      <view class="tab-container">
        <view
          @click="checkNav(0)"
          :class="['tab-item', navCheck === 0 ? 'tab-active' : '']"
        >
          <text class="tab-text">权益卡订单</text>
          <view v-if="navCheck === 0" class="tab-indicator"></view>
        </view>
        <view
          @click="checkNav(1)"
          :class="['tab-item', navCheck === 1 ? 'tab-active' : '']"
        >
          <text class="tab-text">ETC商城订单</text>
          <view v-if="navCheck === 1" class="tab-indicator"></view>
        </view>
        <view
          @click="checkNav(2)"
          :class="['tab-item', navCheck === 2 ? 'tab-active' : '']"
        >
          <text class="tab-text">ETC自营商品订单</text>
          <view v-if="navCheck === 2" class="tab-indicator"></view>
        </view>
        <view
          @click="checkNav(3)"
          :class="['tab-item', navCheck === 3 ? 'tab-active' : '']"
        >
          <text class="tab-text">话费订单</text>
          <view v-if="navCheck === 3" class="tab-indicator"></view>
        </view>
      </view>
    </view>
    <!-- 订单内容区域 -->
    <view class="content-wrapper">
      <!-- 权益卡订单 -->
      <view v-if="navCheck == '0'" class="order-list">
        <view v-if="showData.length != 0">
          <view
            class="order-card"
            v-for="(data, index) in showData"
            :key="index"
          >
            <!-- 订单头部 -->
            <view class="order-header">
              <view class="order-title">
                <uni-icons type="gift" size="16" color="#00d4aa"></uni-icons>
                <text class="title-text">
                  {{
                    dict[data.equityType] === undefined
                      ? "权益卡订单"
                      : dict[data.equityType]
                  }}
                </text>
              </view>
              <view :class="['status-badge', getStatusClass(data)]">
                {{ getStatus(data) }}
              </view>
            </view>

            <!-- 订单信息 -->
            <view class="order-info">
              <view class="info-row">
                <text class="info-label">订单号</text>
                <text class="info-value">{{ data.orderno }}</text>
                <view class="copy-link" @click="copy(data.orderno)">
                  <uni-icons type="copy" size="14" color="#00d4aa"></uni-icons>
                  <text style="margin-left: 4px; font-size: 12px; color: #00d4aa;">复制</text>
                </view>
              </view>
              <view class="info-row">
                <text class="info-label">下单时间</text>
                <text class="info-value">{{ formatDate(data.orderTime) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">实付金额</text>
                <text class="info-price">¥{{ data.orderMoney }}</text>
              </view>
            </view>


          </view>
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-state">
          <uni-icons type="list" size="60" color="#ccc"></uni-icons>
          <text class="empty-text">暂无权益卡订单</text>
          <text class="empty-desc">您还没有任何权益卡订单记录</text>
        </view>
      </view>

      <!-- ETC商城订单 -->
      <view v-else-if="navCheck == '1'" class="order-list">
        <view v-if="qyscOrder.length != 0">
          <view
            class="order-card"
            v-for="(data, index) in qyscOrder"
            :key="index"
          >
            <!-- 订单头部 -->
            <view class="order-header">
              <view class="order-title">
                <uni-icons type="shop" size="16" color="#00d4aa"></uni-icons>
                <text class="title-text">{{ data.orderName }}</text>
              </view>
              <view :class="['status-badge', getMallStatusClass(data.status)]">
                {{ makeStatus(data.status) }}
              </view>
            </view>

            <!-- 订单信息 -->
            <view class="order-info">
              <view class="info-row">
                <text class="info-label">订单号</text>
                <text class="info-value">{{ data.orderno }}</text>
                <view class="copy-link" @click="copy(data.orderno)">
                  <uni-icons type="copy" size="14" color="#00d4aa"></uni-icons>
                  <text style="margin-left: 4px; font-size: 12px; color: #00d4aa;">复制</text>
                </view>
              </view>
              <view class="info-row">
                <text class="info-label">完成时间</text>
                <text class="info-value">{{
                  formatDate1(data.completeDate)
                }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">实付金额</text>
                <text class="info-price">¥{{ data.customerPrice }}</text>
              </view>
            </view>


          </view>
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-state">
          <uni-icons type="shop" size="60" color="#ccc"></uni-icons>
          <text class="empty-text">暂无ETC商城订单</text>
          <text class="empty-desc">您还没有任何ETC商城订单记录</text>
        </view>
      </view>

      <!-- ETC自营商品订单 -->
      <view v-else-if="navCheck == '2'" class="order-list">
        <view v-if="selfGoodsOrders.length != 0">
          <view
            class="order-card"
            v-for="(data, index) in selfGoodsOrders"
            :key="index"
          >
            <!-- 订单头部 -->
            <view class="order-header">
              <view class="order-title">
                <uni-icons type="goods" size="16" color="#00d4aa"></uni-icons>
                <text class="title-text"
                  >{{ data.goodsName }} ⅹ {{ data.goodsNum }}</text
                >
              </view>
              <view
                :class="[
                  'status-badge',
                  getSelfGoodsStatusClass(data.orderStatus),
                ]"
              >
                {{ formatSelfGoodsStatus(data.orderStatus) }}
              </view>
            </view>

            <!-- 订单信息 -->
            <view class="order-info">
              <view class="info-row">
                <text class="info-label">订单号</text>
                <text class="info-value">{{ data.outTradeNum }}</text>
                <view class="copy-link" @click="copy(data.outTradeNum)">
                  <uni-icons type="copy" size="14" color="#00d4aa"></uni-icons>
                  <text style="margin-left: 4px; font-size: 12px; color: #00d4aa;">复制</text>
                </view>
              </view>
              <view class="info-row">
                <text class="info-label">邮寄单号</text>
                <text class="info-value">{{
                  data.mailNo ? data.mailNo : "无"
                }}</text>
                <view
                  v-if="data.mailNo"
                  class="copy-link"
                  @click="copy(data.mailNo)"
                >
                  <uni-icons type="copy" size="14" color="#00d4aa"></uni-icons>
                  <text style="margin-left: 4px; font-size: 12px; color: #00d4aa;">复制</text>
                </view>
              </view>
              <view class="info-row">
                <text class="info-label">支付时间</text>
                <text class="info-value">{{ formatDate1(data.payTime) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">抵扣积分</text>
                <text class="info-highlight">{{ data.usedIntegral || 0 }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">商品原价</text>
                <text class="info-value">¥{{ data.facePrice }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">抵扣后金额</text>
                <text class="info-value"
                  >¥{{ (data.realPay - data.freight).toFixed(2) }}</text
                >
              </view>
              <view class="info-row">
                <text class="info-label">运费</text>
                <text class="info-value">¥{{ data.freight.toFixed(2) }}</text>
              </view>
              <view class="info-row">
                <text class="info-label">实际支付</text>
                <text class="info-price">¥{{ data.realPay }}</text>
              </view>
            </view>


          </view>
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-state">
          <uni-icons type="goods" size="60" color="#ccc"></uni-icons>
          <text class="empty-text">暂无ETC自营商品订单</text>
          <text class="empty-desc">您还没有任何ETC自营商品订单记录</text>
        </view>
      </view>

      <!-- 话费订单 -->
      <view v-else-if="navCheck == '3'" class="order-list">
        <view v-if="creditOrders.length != 0">
          <view
            class="order-card"
            v-for="(data, index) in creditOrders"
            :key="index"
          >
            <!-- 订单头部 -->
            <view class="order-header">
              <view class="order-title">
                <uni-icons type="phone" size="16" color="#00d4aa"></uni-icons>
                <text class="title-text">话费充值订单</text>
              </view>
              <view :class="['status-badge', getCreditStatusClass(data.orderStatus)]">
                {{ formatCreditStatus(data.orderStatus) }}
              </view>
            </view>

            <!-- 订单内容 -->
            <view class="order-content">
              <view class="order-info">
                <view class="info-row">
                  <text class="info-label">充值手机号：</text>
                  <text class="info-value">{{ data.mobile }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">面值金额：</text>
                  <text class="info-value price">¥{{ data.facePrice }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">实付金额：</text>
                  <text class="info-value price">¥{{ data.realPay }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">商品名称：</text>
                  <text class="info-value">{{ data.productTitle }}</text>
                </view>
                <view class="info-row">
                  <text class="info-label">支付时间：</text>
                  <text class="info-value">{{ data.payTime || '未支付' }}</text>
                </view>
                <view class="info-row" v-if="data.outTradeNum">
                  <text class="info-label">订单号：</text>
                  <text class="info-value">{{ data.outTradeNum }}</text>
                  <view class="copy-link" @click="copy(data.outTradeNum)">
                    <uni-icons type="copy" size="14" color="#00d4aa"></uni-icons>
                    <text style="margin-left: 4px; font-size: 12px; color: #00d4aa;">复制</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-state">
          <uni-icons type="phone" size="60" color="#ccc"></uni-icons>
          <text class="empty-text">暂无话费订单</text>
          <text class="empty-desc">您还没有任何话费充值订单记录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getOrdersInfoByStatus } from "@/api/orderqyk.js";
import { allcommodity } from "@/api/shop.js";
import { getqyscOrder, showInformations } from "@/api/user.js";
import { getSelfGoodsOrderList } from "@/api/goods.js";
import { getCreditOrdersByUserId } from "@/subpackages/services/api/credit.js";

const dict = ["", "黄金卡", "钻石卡"];
export default {
  data() {
    return {
      navCheck: 0,
      orderList: {
        wait_pay: [],
        complete: [],
        refund: {
          wait_refund: [],
          refund_failed: [],
          refund_complete: [],
        },
        eqityType: [],
      },
      showData: [],
      dict: {},
      eqityType: {},
      qyscOrder: [],
      selfGoodsOrders: [],
      creditOrders: [], // 话费订单数据
      userInfo: null, // 添加用户信息字段
    };
  },
  methods: {
    // 获取用户信息的通用方法
    async getUserInfo() {
      if (!this.userInfo) {
        const userRes = await showInformations();
        if (userRes.data && userRes.data.code === 200) {
          this.userInfo = userRes.data.data;
          return this.userInfo;
        } else {
          throw new Error("获取用户信息失败");
        }
      }
      return this.userInfo;
    },
    checkNav(index) {
      this.navCheck = index;
      this.showData = [];
      if (index === 0) {
        this.showAll();
        return;
      } else if (index === 1) {
        // this.showAll()
        return;
      } else if (index === 2) {
        this.getSelfGoodsOrderList();
        return;
      }
    },
    async getOrdersInfoByStatus() {
      try {
        const userInfo = await this.getUserInfo();

        const params = {
          userId: userInfo.id,
        };

        const res = await getOrdersInfoByStatus(params);
        const data = res.data;

        data.data.eqityType.forEach((o) => {
          this.eqityType[o.id] = o.name;
        });

        if (data.code !== 200) {
          return;
        }

        this.orderList = data.data;
        this.showAll();
      } catch (err) {
        console.error("获取权益卡订单失败", err);
        this.orderList = {
          wait_pay: [],
          complete: [],
          refund: {
            wait_refund: [],
            refund_failed: [],
            refund_complete: [],
          },
          eqityType: [],
        };
      }
    },
    async getGoodsOrderInfo() {
      try {
        const userInfo = await this.getUserInfo();

        let data = {
          phone: userInfo.phone || userInfo.phnumber,
        };

        const res = await getqyscOrder(data);
        console.log(res);
        this.qyscOrder = res.data.data;
      } catch (err) {
        console.error("获取ETC商城订单失败", err);
        this.qyscOrder = [];
      }
    },
    showAll() {
      this.showData = [];
      this.orderList.wait_pay.forEach((o) => {
        this.showData = [...this.showData, o];
      });
      this.orderList.complete.forEach((o) => {
        this.showData = [...this.showData, o];
      });
      this.orderList.refund.wait_refund.forEach((o) => {
        this.showData = [...this.showData, o];
      });
      this.orderList.refund.refund_failed.forEach((o) => {
        this.showData = [...this.showData, o];
      });
      this.orderList.refund.refund_complete.forEach((o) => {
        this.showData = [...this.showData, o];
      });
      this.showData = this.showData.sort((a, b) => {
        const aa = new Date(a.orderTime).getTime();
        const bb = new Date(b.orderTime).getTime();
        return bb - aa;
      });
      console.log(this.showData);
    },

    getStatus(obj) {
      if (obj.refundStatus === null) {
        if (obj.orderStatus === "COMPLETE") {
          return "已完成";
        } else if (obj.orderStatus === "FAILED") {
          return "已失败";
        } else if (obj.orderStatus === "refund") {
          return "已退款";
        }
        return "已取消";
      }
      if (obj.refundStatus === "WAIT_REFUND") {
        return "待退款";
      } else if (obj.refundStatus === "REFUND") {
        return "已退款";
      }
      return "退款失败";
    },
    formatDate(str = "") {
      const data1 = str.split(" ");
      const data2 = data1[0].split("/");
      return data2[0] + "年" + data2[1] + "月" + data2[2] + "日" + data1[1];
    },
    formatDate1(str = "") {
      if (!str) return "";
      // 原生方法处理时间格式化，保留完整的时分秒
      const data1 = str.split(" ");
      const data2 = data1[0].split("/");
      const timeStr = data1[1] || "00:00:00"; // 如果没有时间部分，默认为00:00:00
      return data2[0] + "年" + data2[1] + "月" + data2[2] + "日 " + timeStr;
    },
    makeStatus(str) {
      console.log(str);
      const statusMap = {
        waitPay: "未支付",
        COMPLETE: "支付成功",
        FAILED: "支付失败",
        success: "成功",
        processing: "处理中",
        failed: "下单失败",
        untreated: "未处理",
        refund: "退款成功",
        "refund-fail": "退款失败",
      };

      return statusMap[str] || str;
    },
    allcommodity() {
      allcommodity().then((res) => {
        res = res.data;
        if (res.code !== 200) {
          return;
        }
        res.data.forEach((o) => {
          this.dict[o.id] = o.name;
        });
        this.dict = {
          ...this.dict,
        };
      });
    },
    async getSelfGoodsOrderList() {
      try {
        const userInfo = await this.getUserInfo();

        const data = {
          userId: userInfo.id,
        };

        const res = await getSelfGoodsOrderList(data);
        this.selfGoodsOrders = res.data.reverse();
      } catch (err) {
        console.error("获取ETC自营商品订单失败", err);
        this.selfGoodsOrders = [];
      }
    },
    // 获取话费订单列表
    async getCreditOrders() {
      try {
        const userInfo = await this.getUserInfo();

        const params = {
          userid: userInfo.id, // 使用showInformations返回的id作为userid
          page: 1,
          limit: 999
        };

        const res = await getCreditOrdersByUserId(params);
        console.log("话费订单数据:", res);

        // 按id倒序排列（与原有逻辑保持一致）
        this.creditOrders = res.data.data.sort((a, b) => {
          return b.id - a.id;
        });
      } catch (err) {
        console.error("获取话费订单失败", err);
        this.creditOrders = [];
      }
    },
    formatSelfGoodsStatus(status) {
      console.log(status);

      const statusMap = {
        WAIT_PAY: "待支付",
        PAY: "支付成功",
        PAY_FAIL: "支付失败",
        WAIT_TOPUP: "充值中",
        FAIL_ORDER: "充值下单失败",
        SUCCESS: "充值成功",
        "refund-success": "退款成功",
        refund: "退款成功",
      };
      return statusMap[status] || status;
    },
    copy(e) {
      // 点击复制
      uni.setClipboardData({
        data: e,
        success: () => {
          uni.showToast({
            title: "复制成功",
            icon: "success",
          });
        },
        fail: () => {
          uni.showToast({
            title: "复制失败",
            icon: "none",
          });
        },
      });
    },
    // 获取订单状态样式类
    getStatusClass(data) {
      const status = this.getStatus(data);
      if (status.includes("已完成")) return "status-success";
      if (status.includes("待退款")) return "status-warning";
      if (status.includes("已退款")) return "status-info";
      if (status.includes("已失败") || status.includes("退款失败"))
        return "status-error";
      return "status-default";
    },
    // 获取商城订单状态样式类
    getMallStatusClass(status) {
      if (status === "success" || status === "COMPLETE")
        return "status-success";
      if (status === "processing" || status === "untreated")
        return "status-warning";
      if (status === "waitPay") return "status-info";
      if (status === "FAILED" || status === "failed") return "status-error";
      if (status === "refund") return "status-info";
      if (status === "refund-fail") return "status-error";
      return "status-default";
    },
    // 获取自营商品订单状态样式类
    getSelfGoodsStatusClass(status) {
      if (status === "SUCCESS") return "status-success";
      if (status === "WAIT_TOPUP" || status === "PAY") return "status-warning";
      if (status === "WAIT_PAY") return "status-info";
      if (status === "PAY_FAIL" || status === "FAIL_ORDER")
        return "status-error";
      if (status === "refund-success") return "status-info";
      return "status-default";
    },
    // 格式化话费订单状态
    formatCreditStatus(orderStatus) {
      const statusMap = {
        WAIT_PAY: "待支付",
        WAIT_TOPUP: "充值中",
        FAIL_ORDER: "充值失败",
        SUCCESS: "充值成功",
        PAY: "支付成功",
        PAY_FAIL: "支付失败",
        REFUND: "退款成功",
        refund: "退款成功",
      };
      return statusMap[orderStatus] || orderStatus;
    },
    // 获取话费订单状态样式类
    getCreditStatusClass(orderStatus) {
      if (orderStatus === "SUCCESS") return "status-success";
      if (orderStatus === "WAIT_TOPUP" || orderStatus === "PAY") return "status-warning";
      if (orderStatus === "WAIT_PAY") return "status-info";
      if (orderStatus === "FAIL_ORDER" || orderStatus === "PAY_FAIL") return "status-error";
      return "status-default";
    },

  },
  onLoad() {
    this.getGoodsOrderInfo();
    this.getOrdersInfoByStatus();
    this.allcommodity();
    this.getSelfGoodsOrderList();
    this.getCreditOrders();
  },
};
</script>

<style lang="scss">
/* 页面容器 */
.order-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
}

/* 顶部导航栏 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 标签导航栏 */
.tab-navigation {
  background: white;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .tab-container {
    display: flex;
    justify-content: space-around;

    .tab-item {
      position: relative;
      padding: 16px 8px;
      flex: 1;
      text-align: center;
      transition: all 0.3s ease;

      .tab-text {
        font-size: 12px;
        color: #666;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      .tab-indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 3px;
        background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
        border-radius: 2px;
        animation: slideIn 0.3s ease;
      }

      &.tab-active {
        .tab-text {
          color: #00d4aa;
          font-weight: 600;
        }
      }
    }
  }
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 30px;
    opacity: 1;
  }
}

/* 内容区域 */
.content-wrapper {
  padding: 20px;
}

.order-list {
  .order-card {
    background: white;
    border-radius: 16px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    /* 订单头部 */
    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 20px 16px;
      border-bottom: 1px solid #f5f5f5;

      .order-title {
        display: flex;
        align-items: center;
        flex: 1;

        .title-text {
          margin-left: 8px;
          font-size: 16px;
          font-weight: 600;
          color: #333;
          line-height: 1.4;
        }
      }
    }

    /* 订单信息 */
    .order-info {
      padding: 16px 20px;

      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        position: relative;

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
          min-width: 80px;
        }

        .info-value {
          font-size: 14px;
          color: #333;
          flex: 1;
          text-align: right;
          word-break: break-all;
        }

        .info-price {
          font-size: 16px;
          color: #00d4aa;
          font-weight: 600;
        }

        .info-highlight {
          font-size: 14px;
          color: #ff6b35;
          font-weight: 600;
        }

        .copy-link {
          margin-left: 8px;
          padding: 3px 6px;
          border-radius: 4px;
          background-color: rgba(0, 212, 170, 0.08);
          border: 1px solid rgba(0, 212, 170, 0.2);
          display: flex;
          align-items: center;
          transition: all 0.2s ease;

          &:active {
            background-color: rgba(0, 212, 170, 0.15);
            transform: scale(0.96);
          }
        }
      }
    }

    /* 订单底部 */
    .order-footer {
      padding: 16px 20px 20px;
      border-top: 1px solid #f5f5f5;
      display: flex;
      justify-content: flex-end;

      .copy-btn {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        background: #f8f9fa;
        border-radius: 20px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;

        text {
          margin-left: 6px;
          font-size: 13px;
          color: #666;
          font-weight: 500;
        }

        &:active {
          background: #e9ecef;
          transform: scale(0.95);
        }
      }
    }
  }
}

/* 状态标签 */
.status-badge {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.3px;

  &.status-success {
    background: rgba(76, 201, 100, 0.1);
    color: #4cd964;
    border: 1px solid rgba(76, 201, 100, 0.2);
  }

  &.status-warning {
    background: rgba(255, 149, 0, 0.1);
    color: #ff9500;
    border: 1px solid rgba(255, 149, 0, 0.2);
  }

  &.status-info {
    background: rgba(0, 212, 170, 0.1);
    color: #00d4aa;
    border: 1px solid rgba(0, 212, 170, 0.2);
  }

  &.status-error {
    background: rgba(255, 59, 48, 0.1);
    color: #ff3b30;
    border: 1px solid rgba(255, 59, 48, 0.2);
  }

  &.status-default {
    background: rgba(142, 142, 147, 0.1);
    color: #8e8e93;
    border: 1px solid rgba(142, 142, 147, 0.2);
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;

  .empty-text {
    font-size: 16px;
    color: #999;
    margin: 20px 0 8px;
    font-weight: 500;
  }

  .empty-desc {
    font-size: 14px;
    color: #ccc;
    line-height: 1.5;
  }
}
</style>
