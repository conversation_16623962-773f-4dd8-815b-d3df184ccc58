<template>
  <view class="user-list-container">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <view class="nav-title">用户列表</view>
      <view class="nav-subtitle">管理您的用户信息和消费数据</view>
    </view>

    <!-- 统计信息栏 -->
    <view class="stats-header">
      <view class="stats-item">
        <view class="stats-label">用户数</view>
        <view class="stats-value">{{ showList.length }}</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-label">消费额</view>
        <view class="stats-value">{{ mySum }} ￥</view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <!-- 空状态 -->
      <view v-if="showList.length === 0" class="empty-state">
        <uni-icons type="person" size="60" color="#ccc"></uni-icons>
        <text class="empty-text">{{ msg }}</text>
        <text class="empty-desc">您还没有任何用户数据</text>
      </view>

      <!-- 用户列表 -->
      <view class="user-list" v-else>
        <view class="user-card" v-for="(data, index) in showList" :key="index">
          <!-- 用户头部 -->
          <view class="user-header">
            <view class="user-title">
              <view class="avatar">
                <image :src="data.headPic" class="avatar-image"></image>
              </view>
              <view class="user-info">
                <view class="user-name">{{ data.userName }}{{ getRealName(data.realName) }}</view>
                <view class="user-date">{{ formatTiem(data.createTime) }}</view>
              </view>
            </view>
          </view>

          <!-- 用户详情 -->
          <view class="user-details">
            <view class="detail-row">
              <text class="detail-label">手机尾号</text>
              <text class="detail-value">{{ getPhoneTail(data.userName) }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">车牌号</text>
              <text class="detail-value">{{ getCaiId(data.carId) }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">消费额</text>
              <text class="detail-price">{{ formationMoney(data.money) }} ￥</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { myUser, myUserList } from "@/subpackages/proxy/api/proxyUserList.js";
import { getUserId, isLogin, isProxyLogin } from "@/utils/auth.js";
import { handlePagePermissionCheck } from "@/utils/permissionChecker.js";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      checkNav: 0,
      performanceObj: {},
      showList: [],
      msg: "加载中",
    };
  },
  methods: {
    formatTiem(timer = "") {
      return timer.split(" ")[0].replaceAll("/", "-");
    },
    getPhoneTail(phone = "") {
      return phone.substring(phone.length - 4);
    },
    getCaiId(carId) {
      if (carId == null) {
        return "/";
      }
      return carId;
    },
    getRealName(realName) {
      if (realName == null) {
        return "";
      }
      return " -- " + realName;
    },
    myUser() {
      myUserList()
        .then((res) => {
          if (res.data.code !== 200) {
            return;
          }
          console.log(res);
          const data = res.data.data;

          this.showList = data;
        })
        .finally(() => {
          this.msg = "暂无用户";
        });
    },
    formationMoney(money = 0) {
      if (money === null) {
        return "0.00";
      }
      return money.toFixed(2);
    },
  },
  computed: {
    ...mapGetters("proxyUser", ["userInfo"]),
    sum() {
      let sum = 0;
      this.showList.forEach((i) => {
        sum += i.money;
      });
      return sum;
    },
    mySum() {
      let sum = 0;
      this.showList.forEach((i) => {
        sum += i.money;
      });
      return sum.toFixed(2);
    },
  },
  onLoad() {
    // 严格的权限检查：基于adminRank的代理端页面权限验证
    if (!handlePagePermissionCheck("proxy", "代理端用户列表")) {
      return; // 权限检查失败，会自动重定向
    }
    this.myUser();
  },
};
</script>

<style lang="scss">
/* 页面容器 - 统一用户端样式 */
.user-list-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
}

/* 顶部导航栏 - 统一用户端样式 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 统计信息栏 - 优化样式 */
.stats-header {
  background: white;
  padding: 20px;
  margin: 0 20px;
  margin-top: -15px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 212, 170, 0.15);
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: relative;
  z-index: 1;

  .stats-item {
    text-align: center;
    flex: 1;

    .stats-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .stats-value {
      font-size: 20px;
      font-weight: 600;
      color: #00d4aa;
      letter-spacing: 0.3px;
    }
  }

  .stats-divider {
    width: 1px;
    height: 40px;
    background-color: #f0f0f0;
    margin: 0 20px;
  }
}

/* 内容区域 */
.content-wrapper {
  padding: 20px;
}

/* 空状态 - 统一用户端样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;

  .empty-text {
    font-size: 16px;
    color: #999;
    margin: 20px 0 8px;
    font-weight: 500;
  }

  .empty-desc {
    font-size: 14px;
    color: #ccc;
    line-height: 1.5;
  }
}

/* 用户列表 */
.user-list {
  .user-card {
    background: white;
    border-radius: 16px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    /* 用户头部 */
    .user-header {
      padding: 20px 20px 16px;
      border-bottom: 1px solid #f5f5f5;

      .user-title {
        display: flex;
        align-items: center;

        .avatar {
          margin-right: 15px;

          .avatar-image {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            border: 2px solid #00d4aa;
            transition: all 0.3s ease;
          }
        }

        .user-info {
          flex: 1;

          .user-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 6px;
            letter-spacing: 0.3px;
          }

          .user-date {
            font-size: 13px;
            color: #666;
            font-weight: 500;
          }
        }
      }
    }

    /* 用户详情 */
    .user-details {
      padding: 16px 20px;

      .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
          min-width: 80px;
        }

        .detail-value {
          font-size: 14px;
          color: #333;
          font-weight: 500;
          text-align: right;
        }

        .detail-price {
          font-size: 16px;
          color: #00d4aa;
          font-weight: 600;
          text-align: right;
        }
      }
    }
  }
}
</style>
