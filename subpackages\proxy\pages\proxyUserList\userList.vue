<template>
  <view class="container">
    <!-- 顶部信息栏 -->
    <view class="stats-header">
      <view class="stats-item">
        <view class="stats-label">用户数</view>
        <view class="stats-value">{{ showList.length }}</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-label">消费额</view>
        <view class="stats-value">{{ mySum }} ￥</view>
      </view>
    </view>

    <!-- 内容区 -->
    <view class="content">
      <view v-if="showList.length === 0" class="empty-message">
        {{ msg }}
      </view>

      <view class="user-card" v-for="(data, index) in showList" :key="index">
        <view class="user-header">
          <view class="avatar">
            <image :src="data.headPic" class="avatar-image"></image>
          </view>
          <view class="user-info">
            <view class="user-name">{{ data.userName }}{{ getRealName(data.realName) }}</view>
            <view class="user-date">{{ formatTiem(data.createTime) }}</view>
          </view>
        </view>

        <view class="divider"></view>

        <view class="user-details">
          <view class="detail-item">
            <view class="detail-label">手机尾号</view>
            <view class="detail-value">{{ getPhoneTail(data.userName) }}</view>
          </view>

          <view class="detail-item">
            <view class="detail-label">车牌号</view>
            <view class="detail-value">{{ getCaiId(data.carId) }}</view>
          </view>

          <view class="detail-item">
            <view class="detail-label">消费额</view>
            <view class="detail-value amount">{{ formationMoney(data.money) }} ￥</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { myUser, myUserList } from "@/subpackages/proxy/api/proxyUserList.js";
import { getUserId, isLogin, isProxyLogin } from "@/utils/auth.js";
import { handlePagePermissionCheck } from "@/utils/permissionChecker.js";
import { mapGetters } from "vuex";
export default {
  data() {
    return {
      checkNav: 0,
      performanceObj: {},
      showList: [],
      msg: "加载中",
    };
  },
  methods: {
    formatTiem(timer = "") {
      return timer.split(" ")[0].replaceAll("/", "-");
    },
    getPhoneTail(phone = "") {
      return phone.substring(phone.length - 4);
    },
    getCaiId(carId) {
      if (carId == null) {
        return "/";
      }
      return carId;
    },
    getRealName(realName) {
      if (realName == null) {
        return "";
      }
      return " -- " + realName;
    },
    myUser() {
      myUserList()
        .then((res) => {
          if (res.data.code !== 200) {
            return;
          }
          console.log(res);
          const data = res.data.data;

          this.showList = data;
        })
        .finally(() => {
          this.msg = "暂无用户";
        });
    },
    formationMoney(money = 0) {
      if (money === null) {
        return "0.00";
      }
      return money.toFixed(2);
    },
  },
  computed: {
    ...mapGetters("proxyUser", ["userInfo"]),
    sum() {
      let sum = 0;
      this.showList.forEach((i) => {
        sum += i.money;
      });
      return sum;
    },
    mySum() {
      let sum = 0;
      this.showList.forEach((i) => {
        sum += i.money;
      });
      return sum.toFixed(2);
    },
  },
  onLoad() {
    // 严格的权限检查：基于adminRank的代理端页面权限验证
    if (!handlePagePermissionCheck("proxy", "代理端用户列表")) {
      return; // 权限检查失败，会自动重定向
    }
    this.myUser();
  },
};
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 30px;
}

.stats-header {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20px 15px;
  background-color: white;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stats-value {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.stats-divider {
  width: 1px;
  height: 40px;
  background-color: #e0e0e0;
  margin: 0 20px;
}

.content {
  padding: 0 15px;
}

.empty-message {
  text-align: center;
  color: #999;
  font-size: 16px;
  margin-top: 100px;
}

.user-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.avatar {
  margin-right: 15px;
}

.avatar-image {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  border: 2px solid #f0f0f0;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.user-date {
  font-size: 14px;
  color: #999;
}

.divider {
  height: 1px;
  background-color: #f0f0f0;
  margin: 15px 0;
}

.user-details {
  display: flex;
  justify-content: space-between;
}

.detail-item {
  text-align: center;
  flex: 1;
}

.detail-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

.detail-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.detail-value.amount {
  color: #ff6b35;
  font-weight: bold;
}
</style>
