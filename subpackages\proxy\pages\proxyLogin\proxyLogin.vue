<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="bg-decoration">
      <view class="bg-circle bg-circle-1"></view>
      <view class="bg-circle bg-circle-2"></view>
      <view class="bg-circle bg-circle-3"></view>
    </view>
    <!-- 返回按钮 -->
    <view class="back-btn" @click="goBackToUser">
      <text class="custom-icon" style="color: #333; font-size: 18px">‹</text>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- Logo和标题区域 -->
      <view class="header-section">
        <view class="logo-container">
          <view class="logo-icon">
            <text class="custom-icon" style="color: #fff; font-size: 40px">👤</text>
          </view>
        </view>
        <view class="welcome-text">代理端登录</view>
        <view class="sub-text">欢迎回来，请登录您的代理人账号</view>
      </view>

      <!-- 登录表单 -->
      <view class="form-container">
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="custom-icon" style="color: #999; font-size: 18px">📞</text>
            </view>
            <input class="form-input" maxlength="11" placeholder="请输入手机号" v-model="loginForm.phone" type="number"
              placeholder-class="input-placeholder" />
          </view>
        </view>
        <view class="input-group">
          <view class="input-wrapper verification-wrapper">
            <view class="input-icon">
              <text class="custom-icon" style="color: #999; font-size: 18px">🔒</text>
            </view>
            <input class="form-input verification-input" maxlength="6" placeholder="请输入验证码"
              v-model="loginForm.verificationCode" type="number" placeholder-class="input-placeholder" />
            <view class="verification-btn" :class="{ disabled: verification || isMessage }" @click="sendVerification">
              <text v-if="!verification">获取验证码</text>
              <text v-else>{{ verificationSecond }}s后重发</text>
            </view>
          </view>
        </view>
        <!-- 协议勾选 -->
        <view class="agreement-section">
          <view class="checkbox-wrapper" @click="agreement = !agreement">
            <view class="checkbox" :class="{ checked: agreement }">
              <text v-if="agreement" class="custom-icon" style="color: #fff; font-size: 12px">✓</text>
            </view>
            <text class="agreement-text">
              我已阅读并同意
              <text class="link-text" @click.stop="toUserProtocol">《用户协议》</text>
            </text>
          </view>
        </view>

        <!-- 登录按钮 -->
        <view class="login-btn-wrapper">
          <button class="login-btn" :class="{ loading: isMessage }" @click="checkLogin">
            <text v-if="!isMessage">立即登录</text>
            <text v-else>登录中...</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { validPhone, validCode } from "@/utils/valid.js";
import { sendCode, checkLogin } from "@/subpackages/proxy/api/proxyUser.js";
import {
  setToken,
  setUserId,
  setProxyLoginStatus,
  setProxyToken,
  setProxyUserId,
  resetProxyInfo,
} from "@/utils/auth.js";
import { mapActions } from "vuex";
import config from "@/utils/config.js";

let timer;

export default {
  data() {
    return {
      verification: false,
      verificationSecond: 0,
      loginForm: {
        phone: "",
        verificationCode: "",
        adminRabk: "0",
      },
      isMessage: false,
      // 协议按钮
      agreement: false,
      invitationCode: null,
    };
  },
  methods: {
    // 返回用户端
    goBackToUser() {
      uni.navigateBack({
        delta: 1,
      });
    },

    // 发送手机验证码
    sendVerification() {
      console.log("发起验证码");
      if (this.verification === true || this.isMessage === true) {
        return;
      }

      let data = {
        phone: this.loginForm.phone,
        invitationCode: uni.getStorageSync("invitationCode"),
        adminRabk: 1,
      };

      if (!validPhone(data.phone)) {
        uni.showToast({
          title: "请输入正确的手机号",
          icon: "none",
        });
        return;
      }

      this.isMessage = true;

      // 发送验证码
      sendCode(data)
        .then((res) => {
          if (res.data.code === 200) {
            uni.showToast({
              title: "验证码已发送",
              icon: "success",
            });

            this.verification = true;
            this.verificationSecond = 59;
            clearInterval(timer);
            timer = setInterval(() => {
              this.verificationSecond = this.verificationSecond - 1;
              if (this.verificationSecond <= 0) {
                clearInterval(timer);
                this.verification = false;
              }
            }, 1000);
          } else {
            uni.showToast({
              title: res.data.message || "发送失败",
              icon: "error",
            });
          }
        })
        .catch((err) => {
          uni.showToast({
            title: "网络错误",
            icon: "error",
          });
        })
        .finally(() => {
          this.isMessage = false;
        });
    },

    // 登录
    checkLogin() {
      if (!this.agreement) {
        uni.showToast({
          title: "请勾选协议",
          icon: "none",
        });
        return;
      }
      if (this.isMessage) {
        return;
      }

      // 验证表单
      const data = {
        ...(this.loginForm || {}),
      };

      if (!validPhone(data.phone)) {
        uni.showToast({
          icon: "error",
          title: "手机号格式有误",
        });
        return;
      }

      if (!validCode(data.verificationCode)) {
        uni.showToast({
          icon: "error",
          title: "验证码格式有误",
        });
        return;
      }

      this.isMessage = true;

      // 代理端登录
      checkLogin(data)
        .then((res) => {
          if (res.data.code === 200) {
            uni.showToast({
              title: "登录成功",
              icon: "success",
            });

            // 存储代理端登录状态（使用独立的代理端token存储）
            const userData = res.data.data;

            // 清除可能存在的用户端登录状态
            resetProxyInfo();

            setProxyToken(userData.token);
            setProxyUserId(userData.userId);
            setProxyLoginStatus(true);

            // 登录成功后立即获取用户信息，跳过复杂的登录状态检查
            this["proxyUser/getUserInfo"]({ skipComplexCheck: true })
              .then(() => {
                console.log("[ProxyLogin] 用户信息获取成功");
              })
              .catch((error) => {
                console.log(
                  "[ProxyLogin] 用户信息获取失败，但不影响登录:",
                  error.message
                );
              });

            // 跳转到代理端主页
            uni.reLaunch({
              url: "/pages/proxyIndex/index",
            });
          } else {
            uni.showToast({
              title: res.data.message || "登录失败",
              icon: "error",
            });
          }
        })
        .catch((err) => {
          uni.showToast({
            title: "网络错误",
            icon: "error",
          });
        })
        .finally(() => {
          this.isMessage = false;
        });
    },

    toUserProtocol() {
      uni.navigateTo({
        url: "/subpackages/proxy/pages/proxyUserProtocol/userProtocol",
      });
    },

    // 拨打客服电话
    callService() {
      uni.makePhoneCall({
        phoneNumber: config.CUSTOMER_SERVICE_CONFIG.PHONE_NUMBER,
      });
    },

    // 映射Vuex actions
    ...mapActions(["proxyUser/getUserInfo"]),
  },

  watch: {
    isMessage(c) {
      if (c) {
        uni.showLoading({
          mask: true,
          title: "加载中",
        });
      } else {
        uni.hideLoading();
      }
    },
  },

  onUnload() {
    clearInterval(timer);
    uni.hideLoading();
  },

  onLoad(config) {
    console.log(config);
    if (config.myInvitationCode !== undefined) {
      this.invitationCode = config.myInvitationCode;
    }
  },
};
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: #f5f5f5;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 - 移除复杂动画，保持简洁 */
.bg-decoration {
  display: none;
}

/* 返回按钮 */
.back-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 50px;
  left: 20px;
  z-index: 999;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 212, 170, 0.1);
}

.back-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 1);
}

/* 主要内容区域 */
.main-content {
  padding: 100px 40px 40px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 头部区域 */
.header-section {
  text-align: center;
  margin-bottom: 60px;
  margin-top: 50px;
}

.logo-container {
  margin-bottom: 30px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 4px 20px rgba(0, 212, 170, 0.3);
}

.welcome-text {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  letter-spacing: 1px;
}

.sub-text {
  font-size: 15px;
  color: #666;
  line-height: 1.5;
  font-weight: 500;
}

/* 表单容器 */
.form-container {
  flex: 1;
}

.input-group {
  margin-bottom: 16px;
}

.input-wrapper {
  position: relative;
  background: #fff;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #00d4aa;
}

.verification-wrapper {
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 12px 0 40px;
  font-size: 15px;
  border: none;
  background: transparent;
  color: #333;
  box-sizing: border-box;
}

.verification-input {
  flex: 1;
  padding-right: 110px;
}

.input-placeholder {
  color: #999;
}

.verification-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  padding: 5px 10px;
  background: #00d4aa;
  color: #fff;
  border-radius: 5px;
  font-size: 11px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.verification-btn:active {
  transform: translateY(-50%) scale(0.95);
}

.verification-btn.disabled {
  background: #ccc;
  color: #666;
}

/* 协议区域 */
.agreement-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30px 0;
  transition: all 0.3s ease;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox {
  width: 14px;
  height: 14px;
  border: 1px solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.checkbox.checked {
  background: #00d4aa;
  border-color: #00d4aa;
}

.agreement-text {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.link-text {
  color: #00d4aa;
}

/* 登录按钮 */
.login-btn-wrapper {
  margin: 30px 0 20px;
}

.login-btn {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  color: #fff;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-sizing: border-box;
  letter-spacing: 1px;
}

.login-btn:active {
  transform: scale(0.98);
}

.login-btn.loading {
  opacity: 0.8;
}

/* 移除底部复杂样式，保持简洁 */
</style>
