<template>
  <view class="proxy-home-container">
    <!-- 顶部导航 -->
    <view class="header">
      <view class="user-info">
        <image :src="`${config.BASE_URL}/static/avatar-proxy.png`" class="avatar"></image>
        <view class="user-detail">
          <view class="user-name">代理人张三</view>
          <view class="user-level">高级代理</view>
        </view>
      </view>
      <view class="logout-btn" @click="logout">
        <image :src="`${config.BASE_URL}/static/logout.png`" mode="widthFix"></image>
      </view>
    </view>

    <!-- 数据统计卡片 -->
    <view class="stats-section">
      <view class="stats-card">
        <view class="stats-item">
          <view class="stats-number">168</view>
          <view class="stats-label">本月推广</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">2,580</view>
          <view class="stats-label">累计推广</view>
        </view>
        <view class="stats-item">
          <view class="stats-number">¥12,680</view>
          <view class="stats-label">本月佣金</view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-title">业务功能</view>
      <view class="menu-grid">
        <view class="menu-item" @click="toPage('/pages/proxyPromotion/proxyPromotion')">
          <image :src="`${config.BASE_URL}/static/menu-promotion.png`" class="menu-icon"></image>
          <view class="menu-text">推广管理</view>
        </view>
        <view class="menu-item" @click="toPage('/pages/proxyCommission/proxyCommission')">
          <image :src="`${config.BASE_URL}/static/menu-commission.png`" class="menu-icon"></image>
          <view class="menu-text">佣金管理</view>
        </view>
        <view class="menu-item" @click="toPage('/pages/proxyCustomer/proxyCustomer')">
          <image :src="`${config.BASE_URL}/static/menu-customer.png`" class="menu-icon"></image>
          <view class="menu-text">客户管理</view>
        </view>
        <view class="menu-item" @click="toPage('/pages/proxyData/proxyData')">
          <image :src="`${config.BASE_URL}/static/menu-data.png`" class="menu-icon"></image>
          <view class="menu-text">数据统计</view>
        </view>
        <view class="menu-item" @click="toPage('/pages/proxyMaterial/proxyMaterial')">
          <image :src="`${config.BASE_URL}/static/menu-material.png`" class="menu-icon"></image>
          <view class="menu-text">推广素材</view>
        </view>
        <view class="menu-item" @click="toPage('/pages/proxyWithdraw/proxyWithdraw')">
          <image :src="`${config.BASE_URL}/static/menu-withdraw.png`" class="menu-icon"></image>
          <view class="menu-text">提现管理</view>
        </view>
      </view>
    </view>

    <!-- 最近动态 -->
    <view class="activity-section">
      <view class="activity-title">最近动态</view>
      <view class="activity-list">
        <view class="activity-item">
          <view class="activity-dot"></view>
          <view class="activity-content">
            <view class="activity-text">用户 138****1234 完成ETC办理</view>
            <view class="activity-time">2分钟前</view>
          </view>
          <view class="activity-amount">+¥50</view>
        </view>
        <view class="activity-item">
          <view class="activity-dot"></view>
          <view class="activity-content">
            <view class="activity-text">用户 159****5678 完成话费充值</view>
            <view class="activity-time">15分钟前</view>
          </view>
          <view class="activity-amount">+¥8</view>
        </view>
        <view class="activity-item">
          <view class="activity-dot"></view>
          <view class="activity-content">
            <view class="activity-text">用户 187****9012 完成加油服务</view>
            <view class="activity-time">1小时前</view>
          </view>
          <view class="activity-amount">+¥15</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { clearProxyLoginStatus } from "@/utils/auth.js";
import config from "@/utils/config.js";

export default {
  data() {
    return {
      config,
    };
  },
  methods: {
    // 页面跳转
    toPage(url) {
      uni.showToast({
        title: "功能开发中",
        icon: "none",
      });
      // uni.navigateTo({
      //   url: url
      // });
    },

    // 退出登录
    logout() {
      uni.showModal({
        title: "提示",
        content: "确定要退出代理端吗？",
        success: (res) => {
          if (res.confirm) {
            // 清除代理端登录状态
            clearProxyLoginStatus();
            uni.removeStorageSync("proxyToken");
            uni.removeStorageSync("proxyUserInfo");

            // 跳转到统一登录页面
            uni.reLaunch({
              url: "/pages/unifiedLogin/unifiedLogin",
            });
          }
        },
      });
    },
  },

  onLoad() {
    // 重定向到代理端首页
    uni.reLaunch({
      url: "/pages/proxyIndex/index",
    });
  },
};
</script>

<style lang="scss" scoped>
.proxy-home-container {
  min-height: 100vh;
  background: #f5f6fa;
  padding: 0 30rpx;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 60rpx 0 40rpx 0;

  .user-info {
    display: flex;
    align-items: center;

    .avatar {
      width: 100rpx;
      height: 100rpx;
      border-radius: 50%;
      margin-right: 24rpx;
    }

    .user-detail {
      .user-name {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 8rpx;
      }

      .user-level {
        font-size: 12px;
        background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        display: inline-block;
        font-weight: 500;
        letter-spacing: 0.2px;
      }
    }
  }

  .logout-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      width: 40rpx;
    }
  }
}

.stats-section {
  margin-bottom: 40rpx;

  .stats-card {
    background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
    border-radius: 20px;
    padding: 25px;
    display: flex;
    justify-content: space-around;
    box-shadow: 0 4px 20px rgba(0, 212, 170, 0.3);

    .stats-item {
      text-align: center;
      color: white;

      .stats-number {
        font-size: 44rpx;
        font-weight: bold;
        margin-bottom: 12rpx;
      }

      .stats-label {
        font-size: 24rpx;
        opacity: 0.9;
      }
    }
  }
}

.menu-section {
  margin-bottom: 40rpx;

  .menu-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .menu-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30rpx;

    .menu-item {
      background: white;
      border-radius: 16rpx;
      padding: 40rpx 20rpx;
      text-align: center;
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

      &:active {
        transform: scale(0.98);
      }

      .menu-icon {
        width: 60rpx;
        height: 60rpx;
        margin-bottom: 16rpx;
      }

      .menu-text {
        font-size: 24rpx;
        color: #333;
      }
    }
  }
}

.activity-section {
  .activity-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .activity-list {
    background: white;
    border-radius: 16rpx;
    padding: 20rpx;

    .activity-item {
      display: flex;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .activity-dot {
        width: 16rpx;
        height: 16rpx;
        background: #667eea;
        border-radius: 50%;
        margin-right: 24rpx;
      }

      .activity-content {
        flex: 1;

        .activity-text {
          font-size: 28rpx;
          color: #333;
          margin-bottom: 8rpx;
        }

        .activity-time {
          font-size: 22rpx;
          color: #999;
        }
      }

      .activity-amount {
        font-size: 28rpx;
        color: #ff6b6b;
        font-weight: bold;
      }
    }
  }
}
</style>
