import {
  get,
  post
} from '@/utils/requestMethod'

const pageByPramas = function(data) {
  return get('/orderqyk/pageByPramas', data)
}
const searchOrderByPage = function(data) {
  return post('/orderqyk/searchOrderByPage', data)
}
// 提现订单分页
const withdrawPage = function(data) {
  return get('/orderwithdraw/pageByPramas', data)
}
// 查询所有钱包list
const queryWalletList = function(data) {
  return get('/wallet/loadAll', data)
}
// 银盛转账
const transferInner = function(data) {
  return post('/ysPay/transferInner', data)
}
// 银盛转账到平台
const transferToPInner = function(data) {
  return post('/ysPay/transferToPInner', data)
}
// 银盛提现
const walletWithdraw = function(data) {
  return post('/ysPay/walletWithdraw', data)
}
// 查询支付平台单个结果
const queryPayPlatform = function(data) {
  return get('/payplatform/loadObjectByPramas', data)
}
// 修改支付平台
const updPlatform = function(data) {
  return get('/payplatform/updPlatform', data)
}
// 获取权益卡订单excel数据
const getQykExcel = function(data) {
  return post('/orderqyk/getExcel', data, 5 * 60 * 1000)
}

const allcommodity = function() {
  return post('/equitytype/allcommodity')
}

export {
  pageByPramas,
  allcommodity,
  searchOrderByPage,
  withdrawPage,
  queryWalletList,
  transferInner,
  transferToPInner,
  walletWithdraw,
  queryPayPlatform,
  updPlatform,
  getQykExcel
}
