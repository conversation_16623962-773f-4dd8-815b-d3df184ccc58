<template>
	<view class="bkcolor" style="overflow: hidden; min-height: 10vh;">
		<!-- 聊天区 -->
		<!-- #ifdef APP-PLUS -->
		<scroll-view style="height: 90vh;" scroll-y :scroll-top="scrollTop" @scroll="scroll">
		<!-- #endif -->
			<!-- #ifndef APP-PLUS -->
			<scroll-view style="height: calc(90vh );" scroll-y :scroll-top="scrollTop" @scroll="scroll">
			<!-- #endif -->
				<view class="wd" style="padding-bottom: 70px;" id="chat-wrap">
					<view :class="data.type === 'admin' ? 'service-admin' : 'service-user'"
						v-for="(data, index) in chatList" :key="index" style="margin-top: 20px; display: flex;">
						<view>
							<image style="height: 30px; width: 30px;" :src="`${config.BASE_URL}/static/service.png`"
								v-if="data.type === 'admin'"></image>
							<image style="height: 30px; width: 30px;" :src="`${config.BASE_URL}/static/user.png`" v-else></image>
						</view>
						<view style="word-break:break-all; word-wrap:break-word; margin: 0px 10px; width: 60vw;"
							class="chat-item-content">
							{{data.text}}
						</view>
					</view>
				</view>
			</scroll-view>
			<!-- 输入框 -->
			<!-- #ifdef APP-PLUS -->
			<view style="position: fixed; bottom: 0; background-color: #EDEDED;width: 100vw;">
				<view class="wd" style="display: flex; justify-content: space-between; padding-top: 5px; height: 10vh;">
					<textarea fixed maxlength="-1" style="width: 100vw; max-height: 10vh;" v-model="text"></textarea>
					<view
						style="align-self: flex-end; margin-bottom: 10px; background-color: #459DF3; padding: 5px 10px; color: white; border-radius: 5px; margin-left: 15px; width: 3rem; text-align: center;"
						@click="sendMessage">
						发送
					</view>
				</view>
			</view>
			<!-- #endif -->
			<!-- #ifndef APP-PLUS -->
			<view style="position: fixed; bottom: 0px; background-color: #EDEDED; width: 100vw;">
				<view class="wd"
					style="display: flex; justify-content: space-between; align-items: center; padding-top: 5px; height: 10vh;">
					<view style="width: 80%;height: 100%; display: flex; align-items: center;">
						<textarea fixed maxlength="-1"
							style="width: 60vw; height: 30px;border-radius: 10px; max-height: 10vh;background-color: #fff;margin: 0 auto;padding: 10px;"
							v-model="text"></textarea>
					</view>
					<view
						style="background-color: #459DF3; padding: 10px 10px; color: white; border-radius: 5px; width: 3rem; text-align: center;"
						@click="sendMessage">
						发送
					</view>
				</view>
			</view>
			<!-- #endif -->
	</view>
</template>

<script>
	import {
		BASE_URL
	} from '@/utils/requset.js'
	import {
		getToken,
		getUserId
	} from '@/utils/auth.js'
	import {
		getOnlineAdminId
	} from '@/api/im.js'
	import {
		mapGetters
	} from 'vuex'
	import config from "@/utils/config.js";
	// 定义聊天类
	function Chat(type = 'user', text = '') {
		this.type = type
		this.text = text
	}

	function SocketMessage(from = '', to = '', message = '') {
		this.from = from
		this.to = to
		this.message = message
	}


	// 更新聊天缓存
	function saveChatList(chatList) {
		const str = JSON.stringify(chatList)
		uni.setStorage({
			key: 'chatList',
			data: JSON.stringify(chatList)
		})
	}

	export default {
		data() {
			return {
				config,
				text: '',
				chatList: [],
				to: '',
				scollHeight: 0,
				scrollTop: 0,
				scrollTopOld: 0,
				bottomHeight: 0
			}
		},
		computed: {
			...mapGetters(['userInfo']),
		},
		methods: {
			getTo() {
				getOnlineAdminId().then(res => {
					console.log(res)
					res = res.data
					if (res.code !== 200) {
						this.chatList = [...this.chatList, new Chat('admin', '暂无客服在线，请稍后在试')]
						this.toBottom()
						return
					}
					this.to = res.data
				})
			},
			initWebsocket() {
				try {
					this.chatList = JSON.parse(uni.getStorageSync('chatList'))
					if (!this.chatList) {
						saveChatList([])
					}
				} catch {
					this.chatList = []
				}
				uni.showLoading({
					title: '连接客服中',
					mask: true
				})
				const socketTask = uni.connectSocket({
					url: BASE_URL.replace("http", "ws") + '/api/websocket/' + this.userInfo.id,
					complete: () => {},
					header: {
						token: getToken()
					}
				})
				uni.onSocketOpen(() => {
					console.log('连接成功！')
					uni.hideLoading()
					uni.showToast({
						title: '连接成功',
						icon: 'success',
						duration: 1000
					})
				})
				uni.onSocketError(() => {
					console.log('连接失败')
					uni.hideLoading()
					uni.showToast({
						title: '连接失败',
						icon: 'error',
						duration: 1000
					})
				})
				// 接收服务器回来的参数
				uni.onSocketMessage((res) => {
					const text = JSON.parse(res.data).message
					this.chatList = [...this.chatList, new Chat('admin', text)]
					saveChatList(this.chatList)
					this.toBottom()
				})
			},
			sendMessage() {
				if (this.to === '') {
					this.getTo()
					return
				}
				if (this.text === '') {
					return
				}
				console.log(this.text)
				const data = new Chat('user', this.text)
				this.chatList = [...this.chatList, data]
				const socketObj = new SocketMessage(this.userInfo.id, this.to, this.text)
				uni.sendSocketMessage({
					data: JSON.stringify(socketObj)
				})
				saveChatList(this.chatList)
				this.text = ''
				this.toBottom()
			},
			scroll(e) {
				this.scrollTopOld = e.detail.scrollTop
			},
			// 去底部
			toBottom() {
				this.$nextTick(() => {
					uni.createSelectorQuery().in(this).select('#chat-wrap').boundingClientRect((data) => {
						this.scrollTop = this.scrollTopOld
						this.scrollTop = data.height
					}).exec()
				})
			}
		},
		onLoad() {

			this.getTo()
			this.initWebsocket()
		},
		onShow() {
			this.toBottom()
		}
	}
</script>

<style>
	.service-user {
		flex-direction: row-reverse;
	}

	.service-admin {}

	.service-admin .chat-item-content {
		background-color: white;
	}

	.service-user .chat-item-content {
		background-color: white;
	}

	.chat-item-content {
		padding: 5px 10px;
		border-radius: 5px;
	}
</style>