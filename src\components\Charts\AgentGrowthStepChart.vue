<template>
  <div class="chart-container">
    <canvas ref="chartCanvas"></canvas>
  </div>
</template>

<script>
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  LineController,
  Title,
  Tooltip,
  Legend,
  Filler
);

export default {
  name: "AgentGrowthStepChart",
  props: {
    echartData: {
      type: Array,
      default: () => [],
    },
    label: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    echartData: {
      handler() {
        this.updateChart();
      },
      deep: true,
    },
    label: {
      handler() {
        this.updateChart();
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.destroy();
    }
  },
  methods: {
    initChart() {
      const ctx = this.$refs.chartCanvas.getContext("2d");

      this.chart = new ChartJS(ctx, {
        type: "line",
        data: {
          labels: this.label.filter((item) => item !== ""),
          datasets: [
            {
              label: "新增代理",
              data: this.echartData.filter((item) => item !== ""),
              fill: false,
              backgroundColor: "rgb(168, 85, 247)",
              borderColor: "rgb(168, 85, 247)",
              borderWidth: 3,
              pointBackgroundColor: "rgb(168, 85, 247)",
              pointBorderColor: "#fff",
              pointBorderWidth: 3,
              pointRadius: 6,
              pointHoverRadius: 8,
              stepped: "after", // 这里创建步骤效果
              tension: 0,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              backgroundColor: "rgba(0, 0, 0, 0.8)",
              titleColor: "#fff",
              bodyColor: "#fff",
              borderColor: "rgb(168, 85, 247)",
              borderWidth: 1,
              callbacks: {
                label: function (context) {
                  return `新增代理: ${context.parsed.y} 人`;
                },
              },
            },
          },
          scales: {
            x: {
              grid: {
                display: false,
              },
              ticks: {
                maxTicksLimit: 8,
                color: "#6b7280",
              },
            },
            y: {
              beginAtZero: true,
              grid: {
                color: "rgba(0, 0, 0, 0.05)",
              },
              ticks: {
                color: "#6b7280",
                stepSize: 1,
                callback: function (value) {
                  return value + " 人";
                },
              },
            },
          },
          interaction: {
            intersect: false,
            mode: "index",
          },
        },
      });
    },
    updateChart() {
      if (this.chart) {
        this.chart.data.labels = this.label.filter((item) => item !== "");
        this.chart.data.datasets[0].data = this.echartData.filter(
          (item) => item !== ""
        );
        this.chart.update();
      }
    },
  },
};
</script>

<style scoped>
.chart-container {
  position: relative;
  height: 200px;
  width: 100%;
}
</style>
