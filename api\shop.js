import { post, get } from '@/utils/requset.js' 


const allcommodity = function() {
	return post("/equitytype/allcommodity")
}

// 创建订单
const saveOrder = function(data) {
	return post('/orderqyk/saveOrder', data)
}

// 微信支付
const AppPayForPrepayid = function(data) {
	return get('/WxPay/AppPayForPrepayid', data)
}

// 检查付款状态
const ifOrderIsComplete = function(data) {
	return post('/orderqyk/ifOrderIsComplete', data)
}

export {
	allcommodity,
	saveOrder,
	AppPayForPrepayid,
	ifOrderIsComplete
}