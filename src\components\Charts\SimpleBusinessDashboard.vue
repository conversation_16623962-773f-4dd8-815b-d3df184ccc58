<template>
  <div class="dashboard-container">
    <div class="stats-grid">
      <!-- 今日用户新增 -->
      <div class="stat-card user-card">
        <div class="stat-icon">
          <i class="el-icon-user"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ userStats.todayUsers }}</div>
          <div class="stat-label">今日新增用户</div>
          <div class="stat-total">总用户: {{ userStats.totalUsers }}</div>
        </div>
      </div>

      <!-- 今日收入 -->
      <div class="stat-card income-card">
        <div class="stat-icon">
          <i class="el-icon-money"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ formatNumber(incomeStats.todayIncome) }}</div>
          <div class="stat-label">今日收入</div>
          <div class="stat-total">总收入: ¥{{ formatNumber(incomeStats.totalIncome) }}</div>
        </div>
      </div>

      <!-- 今日代理新增 -->
      <div class="stat-card agent-card">
        <div class="stat-icon">
          <i class="el-icon-s-custom"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ agentStats.todayAgents }}</div>
          <div class="stat-label">今日新增代理</div>
          <div class="stat-total">总代理: {{ agentStats.totalAgents }}</div>
        </div>
      </div>

      <!-- 今日退款 -->
      <div class="stat-card refund-card">
        <div class="stat-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ formatNumber(incomeStats.todayRefund || 0) }}</div>
          <div class="stat-label">今日退款</div>
          <div class="stat-total">净收入: ¥{{ formatNumber((incomeStats.todayIncome || 0) - (incomeStats.todayRefund || 0)) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SimpleBusinessDashboard',
  props: {
    userStats: {
      type: Object,
      default: () => ({ todayUsers: 0, totalUsers: 0 })
    },
    incomeStats: {
      type: Object,
      default: () => ({ todayIncome: 0, totalIncome: 0, todayRefund: 0 })
    },
    agentStats: {
      type: Object,
      default: () => ({ todayAgents: 0, totalAgents: 0 })
    }
  },
  methods: {
    formatNumber(num) {
      if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
      }
      return num.toLocaleString()
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  width: 100%;
  padding: 1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.user-card .stat-icon {
  background: linear-gradient(135deg, #3B82F6, #1D4ED8);
}

.income-card .stat-icon {
  background: linear-gradient(135deg, #10B981, #047857);
}

.agent-card .stat-icon {
  background: linear-gradient(135deg, #8B5CF6, #7C3AED);
}

.refund-card .stat-icon {
  background: linear-gradient(135deg, #F59E0B, #D97706);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: bold;
  color: #1F2937;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6B7280;
  margin-bottom: 0.5rem;
}

.stat-total {
  font-size: 0.75rem;
  color: #9CA3AF;
}

@media (max-width: 640px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 1rem;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .stat-value {
    font-size: 1.5rem;
  }
}
</style>
