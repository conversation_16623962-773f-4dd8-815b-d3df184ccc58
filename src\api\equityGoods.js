import { get, post } from "@/utils/requestMethod";
import { allcommodity } from "@/api/order";

/**
 * 获取权益配置列表
 */
const getEquityGoodsList = allcommodity;

/**
 * 根据ID获取权益配置详情
 */
const getEquityGoodsDetail = function (id) {
  return get(`/equitytype/getEquitytype`, { id });
};

/**
 * 添加新的权益配置
 */
const addEquityGoods = function (data) {
  return post("/equitytype/saveEquity", data);
};

/**
 * 更新/编辑权益配置
 */
const updateEquityGoods = function (data) {
  return post("/equitytype/updateEquity", data);
};

/**
 * 删除权益配置
 */
const deleteEquityGoods = function (id) {
  return get("/equitytype/delEquity", { id });
};

export {
  getEquityGoodsList,
  getEquityGoodsDetail,
  addEquityGoods,
  updateEquityGoods,
  deleteEquityGoods,
};
