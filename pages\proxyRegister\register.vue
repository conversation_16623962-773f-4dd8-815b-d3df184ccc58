<template>
  <view class="register-container">
    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 邀请信息 -->
      <view v-if="inviterName !== ''" class="invitation-section">
        <view class="inviter-name">{{ inviterName }}</view>
        <view class="invitation-text">团队邀请你加入</view>
      </view>

      <!-- 注册表单 -->
      <view class="form-container">
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="custom-icon" style="color: #999; font-size: 18px">📞</text>
            </view>
            <input class="form-input" maxlength="11" placeholder="请输入手机号" v-model="registerForm.phone" type="number"
              placeholder-class="input-placeholder" />
          </view>
        </view>
        <view class="input-group">
          <view class="input-wrapper">
            <view class="input-icon">
              <text class="custom-icon" style="color: #999; font-size: 18px">👤</text>
            </view>
            <input class="form-input" placeholder="请输入姓名" v-model="registerForm.name"
              placeholder-class="input-placeholder" />
          </view>
        </view>
        <view class="input-group">
          <view class="input-wrapper verification-wrapper">
            <view class="input-icon">
              <text class="custom-icon" style="color: #999; font-size: 18px">🔒</text>
            </view>
            <input class="form-input verification-input" maxlength="6" placeholder="请输入验证码"
              v-model="registerForm.verificationCode" type="number" placeholder-class="input-placeholder" />
            <view class="verification-btn" :class="{ disabled: verification || isSend }" @click="sendVerification">
              <text v-if="!verification">获取验证码</text>
              <text v-else>{{ verificationSecond }}s后重发</text>
            </view>
          </view>
        </view>
        <!-- 协议勾选 -->
        <view class="agreement-section">
          <view class="checkbox-wrapper" @click="checkoutArg">
            <view class="checkbox" :class="{ checked: agreement }">
              <text v-if="agreement" class="custom-icon" style="color: #fff; font-size: 10px">✓</text>
            </view>
            <text class="agreement-text">
              我已阅读并同意
              <text class="link-text" @click.stop="toUserProtocol">《用户协议》</text>
            </text>
          </view>
        </view>

        <!-- 注册按钮 -->
        <view class="register-btn-wrapper">
          <button class="register-btn" :class="{ loading: isSend }" @click="checkLogin">
            <text v-if="!isSend">立即注册</text>
            <text v-else>注册中...</text>
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { validPhone } from "@/utils/valid.js";
import {
  wxSendCode,
  getInfoByInvitationCode,
} from "@/subpackages/proxy/api/proxyRegister.js";
import { checkLogin } from "@/subpackages/proxy/api/proxyUser.js";
import {
  setToken,
  resetInfo,
  setUserId,
  setProxyLoginStatus,
  setProxyToken,
  setProxyUserId,
  resetProxyInfo,
  setScanLoginFlag,
} from "@/utils/auth.js";

let timer;

export default {
  data() {
    return {
      agreement: false,
      isSend: false,
      verification: false,
      verificationSecond: 0,
      registerForm: {
        phone: "",
        name: "",
        adminRabk: "",
        invitationCode: "",
        verificationCode: "",
      },
      inviterName: "",
    };
  },
  methods: {
    checkoutArg() {
      this.agreement = !this.agreement;
    },
    sendVerification() {
      if (!this.agreement) {
        uni.showToast({
          icon: "error",
          title: "请勾选协议",
        });
        return;
      }
      if (this.isSend) {
        return;
      }
      if (!validPhone(this.registerForm.phone)) {
        uni.showToast({
          title: "手机号错误",
          icon: "error",
        });
        return;
      }
      if (this.registerForm.name === "") {
        uni.showToast({
          title: "请输入姓名",
          icon: "error",
        });
        return;
      }
      if (
        this.registerForm.adminRabk === "" ||
        this.registerForm.invitationCode === ""
      ) {
        uni.showToast({
          title: "请重新扫码进入",
          icon: "error",
        });
        return;
      }
      if (
        this.registerForm.adminRabk === "0" ||
        this.registerForm.adminRabk === "2" ||
        this.registerForm.adminRabk === "3"
      ) {
        uni.showToast({
          title: "二维码有误",
          icon: "error",
        });
        return;
      }
      this.isSend = true;
      wxSendCode(this.registerForm)
        .then((res) => {
          console.log(res);
        })
        .finally(() => {
          this.isSend = false;
          this.verificationSecond = 59;
          this.verification = true;
          clearInterval(timer);
          timer = setInterval(() => {
            this.verificationSecond = this.verificationSecond - 1;
            if (this.verificationSecond <= 0) {
              this.verification = false;
              clearInterval(timer);
            }
          }, 1000);
        });
    },
    checkLogin() {
      if (!this.agreement) {
        uni.showToast({
          icon: "error",
          title: "请勾选协议",
        });
        return;
      }
      if (this.isSend) {
        return;
      }
      if (!validPhone(this.registerForm.phone)) {
        uni.showToast({
          title: "手机号错误",
          icon: "error",
        });
        return;
      }
      if (this.registerForm.name === "") {
        uni.showToast({
          title: "请输入姓名",
          icon: "error",
        });
        return;
      }
      if (
        this.registerForm.adminRabk === "" ||
        this.registerForm.invitationCode === ""
      ) {
        uni.showToast({
          title: "请重新扫码进入",
          icon: "error",
        });
        return;
      }
      if (
        this.registerForm.adminRabk === "0" ||
        this.registerForm.adminRabk === "2" ||
        this.registerForm.adminRabk === "3"
      ) {
        uni.showToast({
          title: "二维码有误",
          icon: "error",
        });
        return;
      }
      if (this.registerForm.verificationCode.length !== 6) {
        uni.showToast({
          icon: "error",
          title: "验证码有误",
        });
        return;
      }
      const data = {
        phone: this.registerForm.phone,
        adminRabk: this.registerForm.adminRabk,
        verificationCode: this.registerForm.verificationCode,
      };
      uni.showLoading({
        mask: true,
        title: "加载中",
      });
      checkLogin(data)
        .then((res) => {
          if (res.data.code !== 200) {
            return;
          }
          const data = res.data.data;

          // 清除之前的登录信息（邀请码会被自动保留）
          resetInfo();

          // 设置代理端登录信息（使用独立的代理端token存储）
          setProxyToken(data.token);
          setProxyUserId(data.userId);
          setProxyLoginStatus(true); // 设置代理端登录状态标识
          setScanLoginFlag(); // 设置扫码登录标识，保护登录状态

          uni.hideLoading();
          uni.reLaunch({
            url: "/pages/proxyIndex/index",
          });
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    // 通过邀请码获取姓名
    getInfoByInvitationCode() {
      getInfoByInvitationCode({
        InvitationCode: this.registerForm.invitationCode,
      }).then((res) => {
        console.log(res);
        res = res.data;
        if (res.code !== 200) {
          return;
        }
        this.inviterName = res.data;
      });
    },
    toUserProtocol() {
      uni.navigateTo({
        url: "/subpackages/proxy/pages/proxyUserProtocol/userProtocol",
      });
    },
  },
  watch: {
    isSend(c) {
      if (c) {
        uni.showLoading({
          mask: true,
          title: "加载中",
        });
      } else {
        uni.hideLoading();
      }
    },
  },
  onLoad(query) {
    const scene = decodeURIComponent(query.scene);
    if (scene) {
      const data = scene.split(",");
      console.log(data);
      this.registerForm = {
        ...this.registerForm,
        invitationCode: data[0],
        adminRabk: data[1],
      };
      console.log(this.registerForm);
      this.getInfoByInvitationCode();
    }
  },
};
</script>

<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  box-sizing: border-box;
}

.main-content {
  width: 100%;
  max-width: 400px;
  background: #fff;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 212, 170, 0.15);
}

/* 邀请信息 */
.invitation-section {
  text-align: center;
  margin-bottom: 40px;
  padding: 20px;
  background: linear-gradient(135deg,
      rgba(0, 212, 170, 0.1) 0%,
      rgba(0, 163, 137, 0.1) 100%);
  border-radius: 15px;
  border: 1px solid rgba(0, 212, 170, 0.2);
}

.inviter-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.invitation-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* 表单容器 */
.form-container {
  width: 100%;
}

.input-group {
  margin-bottom: 16px;
}

.input-wrapper {
  position: relative;
  background: #fff;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within {
  border-color: #00d4aa;
}

.verification-wrapper {
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.form-input {
  width: 100%;
  height: 44px;
  padding: 0 12px 0 40px;
  font-size: 15px;
  border: none;
  background: transparent;
  color: #333;
  box-sizing: border-box;
}

.verification-input {
  padding-right: 110px;
}

.input-placeholder {
  color: #999;
}

.verification-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  padding: 5px 10px;
  background: #00d4aa;
  color: #fff;
  border-radius: 5px;
  font-size: 11px;
  transition: all 0.3s ease;
  white-space: nowrap;

  &:active {
    transform: translateY(-50%) scale(0.95);
  }

  &.disabled {
    background: #ccc;
    color: #666;
  }

  text {
    color: inherit;
  }
}

/* 协议区域 */
.agreement-section {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30px 0;
  transition: all 0.3s ease;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox {
  width: 14px;
  height: 14px;
  border: 1px solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &.checked {
    background: #00d4aa;
    border-color: #00d4aa;
  }
}

.agreement-text {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
  transition: color 0.3s ease;
}

.link-text {
  color: #00d4aa;
}

/* 注册按钮 */
.register-btn-wrapper {
  margin: 30px 0 20px;
}

.register-btn {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  color: #fff;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-sizing: border-box;
  letter-spacing: 1px;

  &:active {
    transform: scale(0.98);
  }

  &.loading {
    opacity: 0.8;
  }

  text {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
