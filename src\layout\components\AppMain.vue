﻿<template>
  <section
    class="flex-1 w-full relative overflow-y-auto overflow-x-hidden bg-white/40 backdrop-blur-xl rounded-b-xl shadow-[0_4px_20px_rgba(0,0,0,0.08),inset_0_1px_0_rgba(255,255,255,0.2)] border border-white/30 border-t-0 scrollbar-thin scrollbar-track-black/10 scrollbar-thumb-black/30 hover:scrollbar-thumb-black/50 scrollbar-track-rounded-[3px] scrollbar-thumb-rounded-[3px] min-h-[calc(100vh-60px)] md:min-h-auto"
  >
    <transition name="fade-transform" mode="out-in">
      <router-view :key="key" />
    </transition>
  </section>
</template>

<script>
export default {
  name: "AppMain",
  computed: {
    key() {
      return this.$route.path;
    },
  },
};
</script>

<style>
/* 页面过渡动画 - 无法用Tailwind替代的自定义动画 */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.4s cubic-bezier(0.55, 0, 0.1, 1);
}
.fade-transform-enter {
  opacity: 0;
  transform: translate3d(30px, 0, 0);
}
.fade-transform-leave-to {
  opacity: 0;
  transform: translate3d(-30px, 0, 0);
}
</style>
