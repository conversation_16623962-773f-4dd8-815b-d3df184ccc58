<template>
  <view class="container">
    <!-- 可滚动表单区域 -->
    <scroll-view scroll-y class="form-scroll" :scroll-top="scrollTop">
      <!-- 获取地址提示 -->
      <view class="address-tip" @click="getWxAddress">
        <text class="tip-text">获取微信收货地址</text>
        <text class="custom-icon" style="color: #ffa726; font-size: 16px"
          >›</text
        >
      </view>

      <!-- 智能识别提示 -->
      <!-- <view class="smart-tip">
        <text class="tip-text">智能识别 粘贴文本，可自动识别姓名/电话/地址</text>
      </view> -->

      <!-- 表单内容 -->
      <view class="form-group">
        <view class="form-item">
          <text class="label">姓名</text>
          <input
            type="text"
            placeholder="姓名"
            class="input"
            v-model="formData.name"
          />
        </view>

        <view class="form-item">
          <text class="label">所在地区</text>
          <picker mode="region" @change="handleRegionChange" class="picker">
            <view class="picker-value">{{ formData.region || "请选择" }}</view>
          </picker>
        </view>

        <view class="form-item">
          <text class="label">详细地址</text>
          <input
            type="text"
            placeholder="请输入详细地址信息"
            class="input"
            v-model="formData.address"
          />
        </view>

        <view class="form-item">
          <text class="label">手机</text>
          <input
            type="number"
            placeholder="手机"
            class="input"
            v-model="formData.phone"
          />
        </view>
      </view>
    </scroll-view>

    <!-- 固定底部按钮 -->
    <view class="fixed-btn" @click="saveAddress">
      <text class="btn-text">保存</text>
    </view>
  </view>
</template>

<script>
import { chooseAddress } from "@/utils/asyncWx.js";
import { getUserId } from "@/utils/auth.js";

import { saveAddress } from "@/subpackages/shop/api/selfGoods.js";
export default {
  data() {
    return {
      scrollTop: 0,
      formData: {
        name: "",
        region: "",
        address: "",
        phone: "",
        addressData: {},
      },
      type: "save",
    };
  },
  onLoad(options) {
    console.log(options);
    if (options.type == "upd") {
      this.type = "upd";
      const item = JSON.parse(options.item);
      this.formData.name = item.recipientName;
      this.formData.region = item.provinceCityDistrict;
      this.formData.address = item.addressDetail;
      this.formData.phone = item.phoneNumber;
    }
  },
  methods: {
    handleBack() {
      uni.navigateBack();
    },
    handleRegionChange(e) {
      this.formData.region = e.detail.value.join("");
    },
    saveAddress() {
      const { name, region, address, phone } = this.formData;

      // 检查是否有空值 - 添加防护以避免null迭代错误
      const formData = this.formData || {};
      const isEmpty = Object.values(formData).some(
        (value) => value === "" || value === null || value === undefined
      );

      if (isEmpty) {
        uni.showToast({
          title: "请填写完整数据",
          icon: "none",
          duration: 2000,
        });
        return; // 阻止提交
      }

      uni.showLoading({
        title: "保存中",
      });
      let data = {
        recipientName: this.formData.name,
        provinceCityDistrict: this.formData.region,
        addressDetail: this.formData.address,
        phoneNumber: this.formData.phone,
        logicalDel: 0,
        userId: getUserId(),
      };
      saveAddress(data)
        .then((res) => {
          uni.hideLoading();
          if (res.data == "saveSuccess") {
            uni.showToast({
              title: "添加成功",
              icon: "success",
            });
            uni.navigateBack();
          } else {
            uni.showToast({
              title: "添加失败",
              icon: "error",
            });
          }
        })
        .catch((err) => {
          uni.hideLoading();
          uni.showToast({
            title: "错误，联系管理员",
            icon: "error",
          });
        });
    },
    // 获取微信地址
    getWxAddress() {
      chooseAddress()
        .then((resAddress) => {
          this.formData.addressData = {
            // 收货人
            consignee: resAddress.userName,
            // 手机号
            mobile: resAddress.telNumber,
            // 省
            province: resAddress.provinceName,
            // 市
            city: resAddress.cityName,
            // 区
            region: resAddress.countyName,
            // 详细地址
            detailAddress: resAddress.detailInfo,
          };
          this.formData.phone = resAddress.telNumber;
          this.formData.name = resAddress.userName;
          this.formData.region =
            resAddress.provinceName +
            resAddress.cityName +
            resAddress.countyName;
          this.formData.address = resAddress.detailInfo;

          uni.showToast({
            title: "地址获取成功",
            icon: "success",
            duration: 1500,
          });
        })
        .catch((error) => {
          console.log("获取微信地址:", error);
          // 用户取消选择地址，这是正常行为，不需要显示错误提示
          if (error.errMsg && error.errMsg.includes("cancel")) {
            console.log("用户取消选择地址");
            return;
          }

          // 其他类型的错误才显示提示
          if (error.errMsg && error.errMsg.includes("auth deny")) {
            uni.showToast({
              title: "需要授权访问地址",
              icon: "none",
              duration: 2000,
            });
          } else if (
            error.errMsg &&
            error.errMsg.includes("system permission denied")
          ) {
            uni.showToast({
              title: "请在微信设置中允许访问地址",
              icon: "none",
              duration: 2000,
            });
          } else {
            uni.showToast({
              title: "获取地址失败，请重试",
              icon: "none",
              duration: 2000,
            });
          }
        });
    },
  },
};
</script>

<style scoped>
.container {
  height: 100vh;
  background-color: #f5f5f5;
}

.form-scroll {
  width: 93%;
  height: calc(100vh - 88rpx - 120rpx);
  /* 100vh - navbar - button */
  padding: 30rpx;
}

.address-tip {
  background: #ffffff;
  height: 88rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  justify-content: space-between;
}

.tip-text {
  font-size: 28rpx;
  color: #ffa726;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

.smart-tip {
  background: #fff5e6;
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}

.form-group {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 0 30rpx;
}

.form-item {
  height: 100rpx;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
}

.label {
  width: 140rpx;
  font-size: 28rpx;
  color: #666666;
  margin-right: 20rpx;
}

.input,
.picker-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.picker {
  width: auto;
}

.fixed-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: #999999 1px solid;
}

.btn-text {
  font-size: 32rpx;
  padding: 20rpx 250rpx 20rpx 250rpx;
  border-radius: 100rpx;
  background-color: #ff5e15;
  color: white;
  font-weight: bold;
}
</style>
