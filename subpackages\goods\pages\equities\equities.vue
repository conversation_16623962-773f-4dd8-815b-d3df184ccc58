<template>
  <view class="bkcolor">
    <view class="top-back">
      <image
        :src="`${config.BASE_URL}/static/vip_kq_banner.png`"
        style="width: 100%"
        mode="widthFix"
      ></image>
    </view>
    <view class="eq-shop-list">
      <view v-if="isSend"> 正在加载中 </view>
      <view
        class="shop-item-wrapper"
        v-for="(data, index) in shopList"
        :key="index"
        v-else
        @click="toGoodInfo(data.product_id, data.image2)"
      >
        <view class="eq-shop-item">
          <image :src="data.image1" mode="widthFix"></image>
          <view class="eq-shop-item-info">
            <view>
              {{ data.product_name }}
            </view>
            <!-- <view>
							价格: {{data.purchase_price}}￥
						</view> -->
          </view>
          <view class="eq-buy">点击购买</view>
        </view>
        <view class="eq-line" v-if="index !== shopList.length - 1"></view>
      </view>
    </view>
    <!-- 订单按钮 -->
    <view class="fixed-order-btn" @click="toflOrder">
      <view>
        <uni-icons type="list" size="20" color="#fa5756"></uni-icons>
      </view>
      <view class="order-text"> 订单 </view>
    </view>
  </view>
</template>

<script>
import { getGoodsList } from "@/api/fuLu.js";
import { isLogin } from "@/utils/auth.js";
import config from "@/utils/config.js";
export default {
  data() {
    return {
      config,
      isSend: false,
      shopList: [],
    };
  },
  methods: {
    getGoodsList() {
      this.isSend = true;
      getGoodsList()
        .then((res) => {
          console.log(res);
          res = res.data;
          if (res.code !== 200) {
            return;
          }
          this.shopList = res.data;
        })
        .finally(() => {
          this.isSend = false;
        });
    },
    toGoodInfo(id, image2) {
      if (!isLogin()) {
        uni.showToast({
          duration: 1000,
          title: "请先登录",
          icon: "error",
        });
        return;
      }
      uni.navigateTo({
        url:
          "/subpackages/goods/pages/goodInfo/goodInfo?product_id=" +
          id +
          "&image2=" +
          image2,
      });
    },
    // 去权益订单页面
    toflOrder() {
      if (!isLogin()) {
        uni.showToast({
          duration: 1000,
          title: "请先登录",
          icon: "error",
        });
        return;
      }
      uni.navigateTo({
        url: "/subpackages/goods/pages/flOrder/flOrder",
      });
    },
  },
  onLoad() {
    this.getGoodsList();
  },
};
</script>

<style lang="scss">
// 页面背景
.bkcolor {
  background-color: #f8f8f8;
  min-height: 100vh;
}

// 顶部横幅
.top-back {
  margin-bottom: 16px;

  image {
    width: 100%;
    display: block;
  }
}

// 商品列表容器
.eq-shop-list {
  width: 90%;
  margin: 0 auto;
  background-color: #ffffff;
  padding: 24px;
  border-radius: 12px;
  min-height: 50vh;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;

  // 加载状态
  > view:first-child {
    color: #999999;
    font-size: 14px;
  }
}

// 商品项包装器
.shop-item-wrapper {
  display: block;
  width: 100%;
}

// 商品项
.eq-shop-item {
  min-height: 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    background-color: #f8f8f8;
    border-radius: 8px;
    margin: 0 -8px;
    padding: 24px 8px;
  }

  image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    margin-right: 24px;
    flex-shrink: 0;
  }
}

// 分割线
.eq-line {
  width: 100%;
  height: 1px;
  background-color: #e0e0e0;
  margin: 16px 0;
}

// 商品信息
.eq-shop-item-info {
  flex: 1;
  margin-right: 16px;

  > view:first-child {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    margin-bottom: 8px;
    line-height: 1.4;
  }

  > view:last-child {
    font-size: 14px;
    color: #666666;
    line-height: 1.3;
  }
}

// 购买按钮
.eq-buy {
  padding: 8px 16px;
  font-size: 12px;
  border-radius: 8px;
  color: #ffffff;
  background-color: #00d4aa;
  text-align: center;
  font-weight: 500;
  min-width: 60px;
  transition: all 0.3s ease;

  &:active {
    background-color: #00a389;
    transform: scale(0.95);
  }
}

// 固定订单按钮
.fixed-order-btn {
  position: fixed;
  bottom: 70px;
  right: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.9);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }

  .order-text {
    color: #fa5756;
    font-size: 12px;
    font-weight: bold;
    margin-top: 4px;
  }
}
</style>
