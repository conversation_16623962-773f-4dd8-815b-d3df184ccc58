import service from './request'

const get = function(url, params, timeout) {
  return service({
    url,
    params,
    method: 'get',
    timeout
  })
}

const post = function(url, data, timeout) {
  return service({
    url,
    data,
    method: 'post',
    timeout
  })
}

const upload = function(url, data) {
  return service({
    url,
    data,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 1000 * 60
  })
}

export {
  get,
  post,
  upload
}
