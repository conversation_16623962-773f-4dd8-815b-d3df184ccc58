import {
  post
} from '@/utils/requestMethod'

/**
 * 获取日注册人数
 * @returns []
 */
const getAllUsersByDay = function() {
  return post('/gly/getAllUsersByDay')
}
/**
 * 获取日金额
 * @returns []
 */
const getAllMoneyByDay = function() {
  return post('/gly/getAllMoneyByDay')
}

const getAllRefundMoneyByDay = function() {
  return post('/gly/getAllRefundMoneyByDay')
}

/**
 * 获取代理数
 * @returns []
 */
const getAllDlByDay = function() {
  return post('/gly/getAllDlByDay')
}

const countAllUsers = function() {
  return post('/gly/countAllUsers')
}

const allIncome = function() {
  return post('/gly/allIncome')
}

const countAllAgent = function() {
  return post('/gly/countAllAgent')
}

export {
  getAllUsersByDay,
  getAllMoneyByDay,
  getAllDlByDay,
  countAllUsers,
  allIncome,
  countAllAgent,
  getAllRefundMoneyByDay
}
