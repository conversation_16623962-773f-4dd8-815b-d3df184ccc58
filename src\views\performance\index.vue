﻿<template>
  <div class="app-container">
    <div style="display: flex; margin-bottom: 8px;">
      <el-date-picker
        v-model="range"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :picker-options="pickerOptions"
      />
      <div style="margin-left: 20px;">
        <el-select v-model="orderType" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div style="margin-left: 20px;">
        <el-button @click="searchItem">查询</el-button>
      </div>
    </div>
    <div class="performance-top">
      <div style="display: flex; justify-content: space-around;">
        <div style="height: 200px; display: flex; flex-direction: column; justify-content: space-around;">
          <div>营业额:{{ moneyFormat(proxyData.allMoney) }}￥</div>
          <div>总数:{{ proxyData.completeCount }}</div>
        </div>
        <div style="display: flex; flex-wrap: wrap; align-items: center;">
          <div v-for="(data, index) in proxyData.goods" :key="index" class="goods-item">
            {{ data.name }}: {{ data.count }}
          </div>
        </div>
      </div>
      <div style="display: flex; justify-content: space-around;">
        <div style="height: 200px; display: flex; flex-direction: column; justify-content: space-around;">
          <div>退款费:{{ moneyFormat(proxyData.FailMoney) }}￥</div>
          <div>总数: {{ proxyData.FailCount }}</div>
        </div>
        <div style="display: flex; flex-wrap: wrap; align-items: center;">
          <div v-for="(data, index) in proxyData.Fail" :key="index" class="goods-item">
            {{ data.name }}: {{ data.count }}
          </div>
        </div>
      </div>
    </div>
    <!-- 订单数据 -->
    <div>
      <el-table
        :data="orderList"
        stripe
        style="width: 98%"
      >
        <el-table-column
          prop="orderno"
          label="订单号"
        />
        <el-table-column
          prop="phone"
          label="手机号"
        />
        <el-table-column
          prop="orderMoney"
          label="价格"
        />
        <el-table-column
          prop="equityType"
          label="商品类型"
        />
        <el-table-column
          prop="orderTime"
          label="下单日期"
        />
        <el-table-column
          label="订单状态"
        >
          <template v-slot="scope">
            {{ getOrderStatus(scope.row.orderStatus) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getInformationsByData, getOrdersDetailsInfoByStatus } from '@/api/performance'
export default {
  data() {
    return {
      filterParam: {
        page: 1,
        limit: 10,
        total: 0,
        range: [new Date(), new Date()],
        orderType: '0'
      },
      proxyData: {
        FailCount: 0,
        FailMoney: 0,
        allMoney: 0,
        count: 0,
        goods: [],
        Fail: []
      },
      range: [new Date(), new Date()],
      orderList: [],
      options: [
        { label: '已完成订单', value: '0' },
        { label: '退款订单', value: '1' }
      ],
      orderType: '0',
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近30天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29)
            picker.$emit('pick', [start, end])
          }
        }]
      }
    }
  },
  mounted() {
    this.getInformationsByData()
    this.getOrdersDetailsInfoByStatus()
  },
  methods: {
    getInformationsByData() {
      const data = {
        userid: this.$route.query.id,
        startTime: (this.filterParam.range[0].toLocaleDateString() + ' 00:00:00').replaceAll('/', '-'),
        endTime: (this.filterParam.range[1].toLocaleDateString() + ' 23:59:59').replaceAll('/', '-')
      }
      getInformationsByData(data).then(res => {
        console.log(res)
        res = res.data
        if (res.code !== 200) {
          return
        }
        this.proxyData = res.data
      })
    },
    changePage(e) {
      this.filterParam.page = e
      this.getUserList()
    },
    searchItem() {
      console.log(this.range)
      this.filterParam.range = this.range
      this.filterParam.orderType = this.orderType
      this.getOrdersDetailsInfoByStatus()
      this.getInformationsByData()
    },
    moneyFormat(obj = 0) {
      return obj.toFixed(2)
    },
    // 获取订单
    getOrdersDetailsInfoByStatus() {
      console.log(this.$route)
      const data = {
        userid: this.$route.query.id,
        startTime: (this.filterParam.range[0].toLocaleDateString() + ' 00:00:00').replaceAll('/', '-'),
        endTime: (this.filterParam.range[1].toLocaleDateString() + ' 23:59:59').replaceAll('/', '-'),
        orderType: this.filterParam.orderType
      }
      getOrdersDetailsInfoByStatus(data).then(res => {
        res = res.data
        if (res.code !== 200) {
          return
        }
        if (this.filterParam.orderType === '0') {
          this.orderList = res.data.allCompleteOrders
        } else {
          this.orderList = res.data.allRefundOrders
        }
      })
    },
    // 订单状态映射
    getOrderStatus(obj) {
      if (obj === 'COMPLETE') {
        return '已完成'
      }
      if (obj === 'FAILED') {
        return '已失败'
      }
      if (obj === 'WAIT_PAY') {
        return '待支付'
      }
    }
  }
}
</script>

<style>
.goods-item {
  padding: 5px 15px;
  background: #E3F3FF;
  border-radius: 5px;
  margin-right: 20px;
}
.performance-top {
  display: flex;
  justify-content: space-around;
  background-color: white;
  box-shadow: 1px 1px 5px 1px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 10px;
  margin-bottom: 8px;
}
.performance-top > div {
  width: 50%;
}
</style>

