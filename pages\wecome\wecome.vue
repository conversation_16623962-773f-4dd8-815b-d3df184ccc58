<template>
  <view class="welcome-container">
    <image :src="`${config.BASE_URL}/static/wecome.png`" class="welcome-image" @click="toMy" mode="aspectFill"></image>

    <!-- 底部进入按钮 -->
    <view class="welcome-footer">
      <button class="enter-btn" @click="toMy">
        <text>进入应用</text>
      </button>
    </view>
  </view>
</template>

<script>
import config from "@/utils/config.js";
export default {
  data() {
    return {
      config,
    };
  },
  methods: {
    toMy() {
      uni.switchTab({
        url: "/pages/my/my",
      });
    },
  },
  async onLoad(options) {
    // 处理邀请码 - 使用统一的工具函数
    const { handlePageInvitationCode } = await import('@/utils/invitationCodeHelper.js');
    handlePageInvitationCode(options, 'Welcome');
  },
};
</script>

<style lang="scss" scoped>
.welcome-container {
  height: 100vh;
  width: 100vw;
  position: relative;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.welcome-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.welcome-footer {
  position: absolute;
  bottom: 80px;
  left: 40px;
  right: 40px;
  z-index: 10;
}

.enter-btn {
  width: 100%;
  height: 50px;
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  color: #fff;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-sizing: border-box;
  letter-spacing: 1px;
  box-shadow: 0 4px 20px rgba(0, 212, 170, 0.3);
}

.enter-btn:active {
  transform: scale(0.98);
  box-shadow: 0 6px 25px rgba(0, 212, 170, 0.4);
}

.enter-btn text {
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}
</style>
