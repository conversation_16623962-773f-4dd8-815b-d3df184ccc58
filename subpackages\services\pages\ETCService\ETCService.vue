<template>
  <view class="passage-record-container">
    <!-- 顶部标题区域 -->
    <view class="header-section">
      <view class="header-content">
        <view class="header-title">【e路畅通·票根】小程序</view>
        <view class="header-subtitle">开发票流程指南</view>
        <view class="header-decoration">
          <!-- 装饰性图标 -->
          <view class="decoration-icon">
            <uni-icons
              type="document"
              size="40"
              color="rgba(255,255,255,0.3)"
            ></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="content-wrapper">
      <!-- 流程步骤 -->
      <view class="steps-container">
        <!-- 第1步 -->
        <view class="step-item">
          <view class="step-header">
            <view class="step-number">第1步：前往小程序并登录</view>
          </view>
          <view class="step-content">
            <text class="step-description"
              >前往【e路畅通·票根】小程序，在首页点击【我的车/ETC】，进入登录页面，完成注册/登录。</text
            >
          </view>
          <view class="step-images">
            <image
              :src="imageUrls.step1"
              mode="aspectFit"
              class="step-image"
              @click="previewImage(imageUrls.step1)"
            />
            <view class="image-tip">点击图片可放大查看</view>
          </view>
        </view>

        <!-- 第2步 -->
        <view class="step-item">
          <view class="step-header">
            <view class="step-number">第2步：绑定卡片</view>
          </view>
          <view class="step-content">
            <text class="step-description"
              >点击【添加卡片】→选择个人卡/单位卡，依次完成ETC所属省份(在我处办理的请选择贵州省)、ETC办理人身份信息、办理人手机号完成验证，选择ETC卡后完成绑定。</text
            >
          </view>
          <view class="step-images">
            <image
              :src="imageUrls.step2"
              mode="aspectFit"
              class="step-image"
              @click="previewImage(imageUrls.step2)"
            />
            <view class="image-tip">点击图片可放大查看</view>
          </view>
        </view>

        <!-- 第3步 -->
        <view class="step-item">
          <view class="step-header">
            <view class="step-number">第3步：关联发票抬头</view>
          </view>
          <view class="step-content">
            <text class="step-description"
              >完成绑卡后点击【去关联抬头】，添加开票信息，完成发票抬头关联。或者，您也可以在票根小程序首页点击【发票抬头】进行设置。</text
            >
          </view>
          <view class="step-images">
            <image
              :src="imageUrls.step3"
              mode="aspectFit"
              class="step-image"
              @click="previewImage(imageUrls.step3)"
            />
            <view class="image-tip">点击图片可放大查看</view>
          </view>
        </view>

        <!-- 第4步 -->
        <view class="step-item">
          <view class="step-header">
            <view class="step-number">第4步：开票</view>
          </view>
          <view class="step-content">
            <text class="step-description"
              >首页点击【我要开票】，选择ETC卡→选择通行行程（开具消费发票）→确认交易记录无误后点击【确定开票】，填写接收邮箱，完成开票。</text
            >
          </view>
          <view class="step-images">
            <image
              :src="imageUrls.step4"
              mode="aspectFit"
              class="step-image"
              @click="previewImage(imageUrls.step4)"
            />
            <view class="image-tip">点击图片可放大查看</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 固定底部按钮 -->
    <view class="fixed-bottom-action">
      <view class="action-btn" @click="goToMiniProgram">
        前往 e路畅通·票根
      </view>
    </view>
  </view>
</template>

<script>
import config from "@/utils/config.js";

export default {
  data() {
    return {};
  },
  computed: {
    // 生成完整的图片URL
    imageUrls() {
      return {
        step1: `${config.BASE_URL}/static/1.png`,
        step2: `${config.BASE_URL}/static/2.png`,
        step3: `${config.BASE_URL}/static/3.png`,
        step4: `${config.BASE_URL}/static/4.png`,
      };
    },
  },
  methods: {
    // 预览图片
    previewImage(currentImage) {
      const imageUrls = [
        this.imageUrls.step1,
        this.imageUrls.step2,
        this.imageUrls.step3,
        this.imageUrls.step4,
      ];

      uni.previewImage({
        current: currentImage,
        urls: imageUrls,
        success: (res) => {
          console.log("图片预览成功");
        },
        fail: (err) => {
          console.error("图片预览失败", err);
          uni.showToast({
            title: "图片预览失败",
            icon: "none",
          });
        },
      });
    },

    // 跳转到小程序
    goToMiniProgram() {
      uni.showModal({
        title: "提示",
        content: "是否跳转到e路畅通·票根小程序？",
        success: (res) => {
          if (res.confirm) {
            uni.navigateToMiniProgram({
              appId: "wx9040bb0d3f910004", // e路畅通票根小程序的appId
              success: (res) => {
                console.log("跳转小程序成功", res);
              },
              fail: (err) => {
                console.error("跳转小程序失败", err);
                uni.showToast({
                  title: "跳转失败，请检查是否已安装微信",
                  icon: "none",
                  duration: 2000,
                });
              },
            });
          }
        },
      });
    },
  },
  onLoad() {},
  onShow() {
    // 原有ETC发票业务逻辑已注释备份
    /*
			uni.showModal({
				title: '提示',
				content: '是否跳转微信小程序',
				success: (res) => {
					if (res.confirm) {
						console.log('跳转微信小程序')
						uni.navigateToMiniProgram({
						  appId: 'gh_c3d5b24a4da3',
						  path: 'pages/index/index',
						  extraData: {
						    'data1': 'test'
						  },
						  success(res) {
						    // 打开成功
						  }
						})
					} else if (res.cancel) {

					}
				}
			})

			return
			// 验证是否有权限
			uni.showLoading({
				title: '加载中'
			})
			ifGivePage().then(res => {
				uni.hideLoading()
				res = res.data
				if(res.code !== 200) {
					uni.showToast({
						icon: 'error',
						title: '暂无开通权益',
						duration: 1500
					})
					return Promise.reject()
				}
				return Promise.resolve()
			}, () => {uni.hideLoading()}).then(_ => {
				console.log("#")
				// 跳转小程序
				uni.showModal({
					title: '提示',
					content: '是否跳转微信小程序',
					success: (res) => {
						if (res.confirm) {
							if(weixinShare) {
								console.log('跳转微信小程序')
								weixinShare.launchMiniProgram({
									id: 'gh_c3d5b24a4da3',
									type: 0,
									path: 'pages/index/index'
								})
							} else {
								console.log('未设置微信相关操作')
							}
						} else if (res.cancel) {

						}
					}
				})
			})

			return

			uni.switchTab({
				url: '/pages/my/my',
				complete: () => {
					// 获取微信分享对象
					let weixinShare = null
					plus.share.getServices(services => {
						for(let i in services) {
							if(services[i].id === 'weixin') {
								weixinShare = services[i]
								break
							}
						}
						if(weixinShare) {
							// 验证是否有权限
							uni.showLoading({
								title: '加载中'
							})
							ifGivePage().then(res => {
								uni.hideLoading()
								res = res.data
								if(res.code !== 200) {
									uni.showToast({
										icon: 'error',
										title: '暂无开通权益',
										duration: 1500
									})
									return Promise.reject()
								}
								return Promise.resolve()
							}, () => {uni.hideLoading()}).then(_ => {
								console.log("#")
								// 跳转小程序
								uni.showModal({
									title: '提示',
									content: '是否跳转微信小程序',
									success: (res) => {
										if (res.confirm) {
											if(weixinShare) {
												console.log('跳转微信小程序')
												weixinShare.launchMiniProgram({
													id: 'gh_c3d5b24a4da3',
													type: 0,
													path: 'pages/index/index'
												})
											} else {
												console.log('未设置微信相关操作')
											}
										} else if (res.cancel) {

										}
									}
								})
							})
						} else {
							console.log('未设置微信相关操作')
						}
					})
				}
			})
			*/
  },
};
</script>

<style lang="scss" scoped>
.passage-record-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px; /* 为固定底部按钮留出空间 */
}

/* 顶部标题区域 */
.header-section {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 20px 20px 60px;
  position: relative;
  overflow: hidden;

  .header-content {
    position: relative;
    z-index: 2;

    .header-title {
      color: white;
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 8px;
      text-align: left;
    }

    .header-subtitle {
      color: rgba(255, 255, 255, 0.9);
      font-size: 16px;
      font-weight: 500;
      text-align: left;
    }

    .header-decoration {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);

      .decoration-icon {
        font-size: 40px;
        opacity: 0.3;
      }
    }
  }

  /* 装饰性背景元素 */
  &::before {
    content: "";
    position: absolute;
    top: -50%;
    right: -20%;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    z-index: 1;
  }

  &::after {
    content: "";
    position: absolute;
    bottom: -30%;
    left: -10%;
    width: 150px;
    height: 150px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 50%;
    z-index: 1;
  }
}

.content-wrapper {
  padding: 0;
  margin-top: -30px;
  position: relative;
  z-index: 10;
}

.steps-container {
  margin-bottom: 20px;
  padding: 0 20px;
}

.step-item {
  background: white;
  border-radius: 16px;
  margin-bottom: 20px;
  padding: 24px 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: none;

  .step-header {
    margin-bottom: 16px;

    .step-number {
      font-size: 18px;
      font-weight: bold;
      color: #2d3748;
      line-height: 1.4;
    }
  }

  .step-content {
    margin-bottom: 20px;

    .step-description {
      font-size: 14px;
      color: #4a5568;
      line-height: 1.6;
      display: block;
    }
  }

  .step-images {
    .image-placeholder {
      background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
      border: 2px dashed #cbd5e0;
      border-radius: 12px;
      padding: 40px 20px;
      text-align: center;
      color: #718096;
      font-size: 14px;
      position: relative;
      overflow: hidden;

      &::before {
        content: "\1F4F1";
        display: block;
        font-size: 24px;
        margin-bottom: 8px;
        opacity: 0.6;
      }
    }

    .step-image {
      width: 100%;
      max-width: 100%;
      height: 200px; /* 添加明确的高度 */
      min-height: 150px; /* 添加最小高度 */
      border-radius: 12px;
      box-sizing: border-box;
      object-fit: contain; /* 添加object-fit属性保持图片比例 */
      cursor: pointer; /* 添加手势指针 */
      transition: all 0.3s ease; /* 添加过渡动画 */

      /* 添加点击效果 */
      &:active {
        transform: scale(0.98);
        opacity: 0.8;
      }

      /* 添加悬停效果（在支持hover的设备上） */
      &:hover {
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      }
    }

    .image-tip {
      text-align: center;
      font-size: 12px;
      color: #999;
      margin-top: 8px;
      opacity: 0.8;
    }
  }
}

.fixed-bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20px;
  padding-bottom: calc(
    20px + env(safe-area-inset-bottom)
  ); /* 适配iPhone底部安全区域 */
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  box-sizing: border-box;

  .action-btn {
    background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
    color: white;
    text-align: center;
    padding: 16px 0;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(0, 212, 170, 0.4);
    width: 100%;
    box-sizing: border-box;
    display: block;
    transition: all 0.3s ease;
    letter-spacing: 1px;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 6px 20px rgba(0, 212, 170, 0.5);
    }
  }
}
</style>
