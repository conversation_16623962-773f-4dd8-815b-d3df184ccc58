﻿<template>
  <div class="app-container">
    <!-- 操作区域 -->
    <div class="action-section">
      <div class="action-card">
        <div class="action-header">
          <i class="el-icon-wallet action-icon" />
          <span class="action-title">提现操作</span>
        </div>

        <div class="action-buttons">
          <el-button
            type="primary"
            icon="el-icon-money"
            @click="openTransfer()"
          >
            转账
          </el-button>
          <el-button
            type="success"
            icon="el-icon-platform-eleme"
            @click="openTransferToP()"
          >
            转账到平台
          </el-button>
          <el-button
            type="warning"
            icon="el-icon-bank-card"
            @click="openWithdraw()"
          >
            提现
          </el-button>
          <el-button
            type="info"
            icon="el-icon-setting"
            @click="platformVisible = true"
          >
            修改支付平台
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <i class="el-icon-s-finance" />
            <span>银盛提现订单</span>
          </div>
        </div>

        <el-table
          :data="showData"
          stripe
          style="width: 100%"
          class="modern-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#374151',
            fontWeight: '600',
          }"
          :row-style="{ height: '60px' }"
        >
          <el-table-column
            prop="orderNo"
            label="订单号"
            align="center"
            min-width="200"
          >
            <template slot-scope="scope">
              <span class="order-no">{{ scope.row.orderNo }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="orderMoney"
            label="提现金额"
            align="center"
            width="140"
          >
            <template slot-scope="scope">
              <span class="amount-text">¥{{ scope.row.orderMoney }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="bankAccount"
            label="提现银行卡"
            align="center"
            min-width="180"
          >
            <template slot-scope="scope">
              <div class="bank-info">
                <i class="el-icon-bank-card bank-icon" />
                <span>{{ scope.row.bankAccount }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="name"
            label="账户所属人"
            align="center"
            min-width="120"
          >
            <template slot-scope="scope">
              <span class="account-owner">{{ scope.row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="提现状态" align="center" width="140">
            <template slot-scope="scope">
              <el-tag
                :type="getOrderStatusType(scope.row.orderStatus)"
                size="small"
                class="status-tag"
              >
                {{ getOrderStatus(scope.row.orderStatus) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column
            prop="orderTime"
            label="提现时间"
            align="center"
            min-width="160"
          >
            <template slot-scope="scope">
              <span class="order-time">{{ scope.row.orderTime }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination
        background
        layout="total, prev, pager, next, jumper"
        :total="filterParam.total"
        :page-size="filterParam.limit"
        :current-page="filterParam.page"
        @current-change="changePage"
      />
    </div>

    <!-- 转账表单 -->
    <el-dialog
      title="发起转账"
      :visible.sync="transferFormVisible"
      class="custom-dialog"
    >
      <el-form
        ref="transferRuleForm"
        :model="transferForm"
        :rules="transferRules"
      >
        <el-form-item
          label="转账金额"
          :label-width="formLabelWidth"
          prop="amt"
          style="margin-right: 30px"
        >
          <el-input
            v-model="transferForm.amt"
            autocomplete="off"
            type="number"
          />
        </el-form-item>
        <el-form-item
          label="收款钱包"
          :label-width="formLabelWidth"
          prop="walletNo"
          style="margin-right: 30px"
        >
          <el-select
            v-model="transferForm.walletNo"
            placeholder="请选择收款钱包"
            style="width: 100%"
          >
            <el-option
              v-for="item in walletList"
              :key="item.merchantNo"
              :label="getWalletLabel(item)"
              :value="item.merchantNo"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="transferFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="tansfer()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 转账到平台（主商户）表单 -->
    <el-dialog title="发起转账" :visible.sync="transferFormToPlatformVisible">
      <el-form
        ref="transferRuleToPForm"
        :model="transferFormToP"
        :rules="transferToPRules"
      >
        <el-form-item
          label="转账金额"
          :label-width="formLabelWidth"
          prop="amt"
          style="margin-right: 30px"
        >
          <el-input
            v-model="transferFormToP.amt"
            autocomplete="off"
            type="number"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="transferFormToPlatformVisible = false"
          >取 消</el-button
        >
        <el-button type="primary" @click="tansferToP()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 提现表单 -->
    <el-dialog title="发起提现" :visible.sync="withdrawFormVisible">
      <el-form
        ref="withdrawRuleForm"
        :model="withdrawForm"
        :rules="withdrawRules"
      >
        <el-form-item
          label="提现金额"
          :label-width="formLabelWidth"
          prop="amt"
          style="margin-right: 30px"
        >
          <el-input
            v-model="withdrawForm.amt"
            autocomplete="off"
            type="number"
          />
        </el-form-item>
        <el-form-item
          label="提现钱包"
          :label-width="formLabelWidth"
          prop="walletNo"
          style="margin-right: 30px"
        >
          <el-select
            v-model="withdrawForm.walletNo"
            placeholder="请选择提现钱包"
            style="width: 100%"
          >
            <el-option
              v-for="item in walletList"
              :key="item.merchantNo"
              :label="getWalletLabel(item)"
              :value="item.merchantNo"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          label="备注"
          :label-width="formLabelWidth"
          prop="remark"
          style="margin-right: 30px"
        >
          <el-input v-model="withdrawForm.remark" autocomplete="off" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="withdrawFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="withdraw()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 验证表单 -->
    <el-dialog title="前往下方链接进行验证" :visible.sync="verifyVisible">
      <el-link type="primary">{{ verifyUrl }}</el-link>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="verifyVisible = false"
          >确 定</el-button
        >
      </div>
    </el-dialog>

    <!-- 修改支付平台表单 -->
    <el-dialog title="修改支付平台" :visible.sync="platformVisible">
      <el-radio
        v-model="platform"
        label="富友"
        style="margin-left: 60px"
        border="true"
        size="medium"
        >富友</el-radio
      >
      <el-radio v-model="platform" label="银盛" border="true" size="medium"
        >银盛</el-radio
      >
      <div slot="footer" class="dialog-footer">
        <el-button @click="platformVisible = false">取 消</el-button>
        <el-button type="primary" @click="updPlatform()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  withdrawPage,
  queryWalletList,
  transferInner,
  transferToPInner,
  walletWithdraw,
  queryPayPlatform,
  updPlatform,
} from "@/api/order";

export default {
  data() {
    return {
      showData: [],
      filterParam: {
        page: 1,
        limit: 10,
        total: 0,
        phone: "",
        orderno: "",
      },
      formLabelWidth: "120px",
      transferFormVisible: false,
      transferForm: {},
      walletList: [],
      walletOption: [],
      transferRules: {
        amt: [{ required: true, message: "请输入转账金额", trigger: "blur" }],
        walletNo: [
          { required: true, message: "请选择收款钱包", trigger: "change" },
        ],
      },
      verifyVisible: false,
      verifyUrl: "",
      withdrawFormVisible: false,
      withdrawForm: {},
      withdrawRules: {
        amt: [{ required: true, message: "请输入提现金额", trigger: "blur" }],
        walletNo: [
          { required: true, message: "请选择提现钱包", trigger: "change" },
        ],
        remark: [{ required: true, message: "请输入备注", trigger: "blur" }],
      },
      platformVisible: false,
      platform: "",
      transferFormToPlatformVisible: false,
      transferFormToP: {},
      transferToPRules: {
        amt: [{ required: true, message: "请输入转账金额", trigger: "blur" }],
      },
    };
  },
  mounted() {
    this.pageByPramas();
    this.queryWallet();
    this.loadPlatform();
  },
  methods: {
    // 获取现在使用支付平台
    loadPlatform() {
      const data = {
        state: "1",
      };
      queryPayPlatform(data).then((res) => {
        this.platform = res.data.name;
      });
    },
    // 修改支付平台
    updPlatform() {
      const data = {
        name: this.platform,
      };
      updPlatform(data).then((res) => {
        if (res.data.message === "success") {
          this.platformVisible = false;
          // 修改支付平台成功，不显示消息提示
        } else {
          this.$message.error(res.data.data);
        }
      });
    },
    // 打开转账弹窗
    openTransfer() {
      this.transferForm = {};
      this.transferFormVisible = true;
    },
    // 打开转账到平台弹窗
    openTransferToP() {
      this.transferFormToP = {};
      this.transferFormToPlatformVisible = true;
    },
    // 打开提现弹窗
    openWithdraw() {
      this.withdrawForm = {};
      this.withdrawFormVisible = true;
    },
    // 获取钱包list
    queryWallet() {
      queryWalletList().then((res) => {
        this.walletList = res.data;
      });
    },
    // 钱包选项映射
    getWalletLabel(item) {
      const res = item.name + "--" + item.mobile + "--" + item.bankAccount;
      return res;
    },
    // 转账
    tansfer() {
      this.$refs[`transferRuleForm`].validate((valid) => {
        if (valid) {
          const data = {
            amt: this.transferForm.amt,
            walletNo: this.transferForm.walletNo,
            transferType: "P2C",
          };
          transferInner(data).then((res) => {
            if (res.data.message === "success") {
              this.transferFormVisible = false;
              this.verifyUrl = res.data.data;
              this.verifyVisible = true;
            } else {
              this.$message.error(res.data.data);
            }
          });
        } else {
          return false;
        }
      });
    },
    // 转账到平台
    tansferToP() {
      this.$refs[`transferRuleToPForm`].validate((valid) => {
        if (valid) {
          const data = {
            amt: this.transferFormToP.amt,
          };
          transferToPInner(data).then((res) => {
            if (res.data.message === "success") {
              this.transferFormToPlatformVisible = false;
              this.verifyUrl = res.data.data;
              this.verifyVisible = true;
            } else {
              this.$message.error(res.data.data);
            }
          });
        } else {
          return false;
        }
      });
    },
    // 提现
    withdraw() {
      this.$refs[`withdrawRuleForm`].validate((valid) => {
        if (valid) {
          const data = {
            amt: this.withdrawForm.amt,
            walletNo: this.withdrawForm.walletNo,
            remark: this.withdrawForm.remark,
          };
          const filter = this.walletList.find(
            (item) => item.merchantNo == data.walletNo
          );
          data.linkId = filter.linkId;
          data.name = filter.name;
          console.log(data);
          walletWithdraw(data).then((res) => {
            if (res.data.message === "success") {
              this.transferFormVisible = false;
              this.verifyUrl = res.data.data;
              this.verifyVisible = true;
            } else {
              this.$message.error(res.data.data);
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    pageByPramas() {
      const data = {
        page: this.filterParam.page,
        limit: this.filterParam.limit,
      };
      // if (this.filterParam.phone !== '') {
      // 	data.mobile = this.filterParam.phone
      // }
      // if (this.filterParam.orderno !== '') {
      // 	data.outTradeNum = this.filterParam.orderno
      // }
      // if (this.filterParam.refundStatus !== '') {
      // 	data.orderStatus = this.filterParam.refundStatus
      // }

      withdrawPage(data).then((res) => {
        console.log(res);
        res = res.data;
        this.filterParam.total = res.total;
        this.showData = res.list;
        console.log(this.showData);
      });
    },
    changePage(page) {
      this.filterParam.page = page;
      this.pageByPramas();
    },
    // 订单状态映射
    getOrderStatus(obj) {
      if (obj === "WAIT_APPLY") {
        return "待申请";
      }
      if (obj === "APPLIED") {
        return "已申请";
      }
      if (obj === "COMPLETE") {
        return "已完成";
      }
      if (obj === "FAILED") {
        return "提现失败";
      }
    },
    // 订单状态类型映射 - 用于el-tag的type属性
    getOrderStatusType(obj) {
      if (obj === "WAIT_APPLY") {
        return "warning";
      }
      if (obj === "APPLIED") {
        return "primary";
      }
      if (obj === "COMPLETE") {
        return "success";
      }
      if (obj === "FAILED") {
        return "danger";
      }
      return "info"; // 默认类型
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 6px;
  background-color: #f5f7fa;
}

/* 操作区域样式 */
.action-section {
  margin-bottom: 8px;
}

.action-card {
  background: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.action-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.action-icon {
  font-size: 20px;
  color: #409eff;
  margin-right: 6px;
}

.action-title {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 8px;
}

.table-card {
  background: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: #303133;
}

.table-title i {
  font-size: 20px;
  color: #409eff;
  margin-right: 6px;
}

.modern-table {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ebeef5;
}

.modern-table .el-table__header-wrapper {
  border-radius: 8px 8px 0 0;
}

.modern-table .el-table__row {
  transition: all 0.3s ease;
}

.modern-table .el-table__row:hover {
  background-color: #f8faff !important;

  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.order-no {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 12px;
  color: #606266;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.amount-text {
  font-weight: 600;
  color: #e6a23c;
  font-size: 0.8rem;
}

.bank-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.bank-icon {
  color: #409eff;
  font-size: 1rem;
}

.account-owner {
  font-weight: 500;
  color: #303133;
}

.status-tag {
  border-radius: 8px;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 500;
}

.order-time {
  color: #909399;
  font-size: 12px;
}

/* 分页区域样式 */
.pagination-section {
  display: flex;
  justify-content: flex-end;
}

/* 对话框样式 */
.custom-dialog .el-dialog {
  border-radius: 8px;
  overflow: hidden;
}

.custom-dialog .el-dialog__header {
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  color: white;
  padding: 8px 10px;
}

.custom-dialog .el-dialog__title {
  color: white;
  font-weight: 600;
}

.custom-dialog .el-dialog__headerbtn .el-dialog__close {
  color: white;
}

.custom-dialog .el-dialog__body {
  padding: 10px;
}

.custom-dialog .el-form-item__label {
  font-weight: 500;
  color: #303133;
}

.custom-dialog .el-input__inner {
}

.custom-dialog .el-select {
  width: 100%;
}

.dialog-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.dialog-footer .el-button {
  padding: 10px 20px;
  font-weight: 500;
}

.download-excel {
  position: absolute;
  right: 3vw;
}
</style>
