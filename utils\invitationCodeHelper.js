/**
 * 邀请码处理工具类
 * 统一管理邀请码的存储、验证和绑定逻辑
 */

import { checkUserInvitation } from "@/api/user.js";

/**
 * 邀请码类型枚举
 */
export const INVITATION_CODE_TYPES = {
  USER: 'user',           // 普通用户邀请码
  PROXY_REGISTER: 'proxy_register'  // 代理注册邀请码
};

/**
 * 解析邀请码类型和内容
 * @param {string} sceneValue - 从二维码中解析出的scene值
 * @returns {object} - { type, data }
 */
export function parseInvitationCode(sceneValue) {
  if (!sceneValue) {
    return { type: null, data: null };
  }

  // 代理注册二维码（格式：邀请码,代理级别）
  if (sceneValue.includes(',')) {
    const parts = sceneValue.split(',');
    return {
      type: INVITATION_CODE_TYPES.PROXY_REGISTER,
      data: {
        invitationCode: parts[0],
        adminRank: parts[1]
      }
    };
  }

  // 普通用户邀请码（纯数字或长数字字符串）
  return {
    type: INVITATION_CODE_TYPES.USER,
    data: {
      invitationCode: sceneValue
    }
  };
}

/**
 * 存储邀请码到本地
 * @param {string} invitationCode - 邀请码
 */
export function storeInvitationCode(invitationCode) {
  if (invitationCode && invitationCode !== 'undefined' && invitationCode !== '') {
    console.log('[InvitationHelper] 存储邀请码:', invitationCode);
    uni.setStorageSync("invitationCode", invitationCode);
    return true;
  }
  return false;
}

/**
 * 获取本地存储的邀请码
 * @returns {string|null} - 邀请码或null
 */
export function getStoredInvitationCode() {
  const code = uni.getStorageSync("invitationCode");
  return code && code !== 'undefined' && code !== '' ? code : null;
}

/**
 * 绑定用户邀请码
 * @param {string} userId - 用户ID
 * @param {string} invitationCode - 邀请码
 * @returns {Promise} - 绑定结果
 */
export function bindUserInvitationCode(userId, invitationCode) {
  if (!userId) {
    return Promise.reject(new Error('用户ID不能为空'));
  }
  
  if (!invitationCode) {
    return Promise.reject(new Error('邀请码不能为空'));
  }

  console.log('[InvitationHelper] 开始绑定邀请码:', { userId, invitationCode });

  return checkUserInvitation({
    userId: userId,
    invitation: invitationCode,
  }).then((res) => {
    console.log('[InvitationHelper] 邀请码绑定结果:', res);
    
    if (res.data && res.data.data) {
      const result = res.data.data;
      return {
        success: result === "绑定成功",
        message: result,
        isRepeat: result === "绑定失败，重复绑定"
      };
    }
    
    return {
      success: false,
      message: "绑定失败",
      isRepeat: false
    };
  });
}

/**
 * 处理页面onLoad中的邀请码
 * @param {object} options - 页面onLoad的options参数
 * @param {string} pageName - 页面名称（用于日志）
 */
export function handlePageInvitationCode(options, pageName = 'Unknown') {
  console.log(`[InvitationHelper] ${pageName} onLoad options:`, options);
  
  if (options && options.scene && options.scene !== 'undefined' && options.scene !== '') {
    console.log(`[InvitationHelper] ${pageName} 检测到邀请码:`, options.scene);
    
    if (storeInvitationCode(options.scene)) {
      console.log(`[InvitationHelper] ${pageName} 邀请码已存储:`, options.scene);
      return options.scene;
    }
  } else {
    console.log(`[InvitationHelper] ${pageName} 未检测到邀请码参数`);
  }
  
  return null;
}

/**
 * 显示邀请码绑定结果的Toast
 * @param {object} result - 绑定结果
 */
export function showBindingResult(result) {
  if (result.success) {
    uni.showToast({
      icon: "success",
      title: "绑定成功",
    });
  } else if (result.isRepeat) {
    uni.showToast({
      icon: "error",
      title: "重复绑定",
    });
  } else {
    uni.showToast({
      icon: "error",
      title: result.message || "绑定失败",
    });
  }
}
