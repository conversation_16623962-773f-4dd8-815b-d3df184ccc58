<template>
  <view class="bkcolor" style="background-color: #f5f6f8">
    <!-- 订单 -->
    <view class="wd">
      <view
        class="my-order-card"
        v-for="(data, index) in orderList"
        :key="index"
      >
        <!-- 订单名 -->
        <view style="display: flex; justify-content: space-between">
          <view>
            {{
              dict[data.equityType] === undefined ? "" : dict[data.equityType]
            }}
          </view>
          <view style="color: red">
            {{ getStatus(data) }}
          </view>
        </view>
        <!-- 订单号 -->
        <view style="color: #7f7f7f; margin-top: 10px; display: flex; align-items: center; justify-content: space-between;">
          <text>订单号: {{ data.orderno }}</text>
          <view class="copy-btn" @click="copy(data.orderno)">
            <uni-icons type="copy" size="12" color="#00d4aa"></uni-icons>
            <text style="margin-left: 4px; font-size: 12px; color: #00d4aa;">复制</text>
          </view>
        </view>
        <view style="margin-top: 10px"> 手机号:{{ data.phone }} </view>
        <view style="margin-top: 10px"> 归属:{{ data.relationship }} </view>
        <view style="margin-top: 10px">
          {{ formatDate(data.orderTime) }}
        </view>
        <view
          style="
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
          "
        >
          <view> 实付:￥{{ data.orderMoney }} </view>
          <view
            style="
              background-color: #00c66f;
              color: white;
              padding: 5px 15px;
              border-radius: 5px;
            "
            v-if="type === '0' && data.refundStatus !== 'WAIT_REFUND'"
            @click="refund(data)"
          >
            申请退款
          </view>
        </view>
      </view>
    </view>
    <view
      v-if="orderList.length === 0 && isSend === false"
      style="
        color: #7f7f7f;
        position: fixed;
        top: 30%;
        left: 50%;
        transform: translateX(-50%);
      "
    >
      <view style="text-align: center"> 暂无订单 </view>
    </view>
    <view
      v-if="isSend === true"
      style="
        color: #7f7f7f;
        position: fixed;
        top: 30%;
        left: 50%;
        transform: translateX(-50%);
      "
    >
      加载中
    </view>
  </view>
</template>

<script>
import {
  getOrdersDetailsInfoByStatus,
  requestARefund,
  allcommodity,
} from "@/subpackages/proxy/api/proxyOrderList.js";
import { getUserId, isLogin, isProxyLogin } from "@/utils/auth.js";
// const dict = ['', '黄金卡', '钻石卡']
export default {
  data() {
    return {
      range: [],
      type: "",
      orderList: [],
      dict: {},
      isSend: false,
    };
  },
  methods: {
    getOrdersDetailsInfoByStatus() {
      const data = {
        userid: getUserId(),
        startTime: this.range[0] + " 00:00:00",
        endTime: this.range[1] + " 23:59:59",
        orderType: this.type,
      };
      this.isSend = true;
      getOrdersDetailsInfoByStatus(data)
        .then((res) => {
          console.log(res);
          res = res.data;
          if (res.code !== 200) {
            return;
          }
          if (this.type === "0") {
            this.orderList = res.data.allCompleteOrders;
          } else {
            this.orderList = res.data.allRefundOrders;
          }
          console.log(this.orderList);
        })
        .finally(() => {
          this.isSend = false;
        });
    },
    getStatus(obj) {
      if (!obj.refundStatus) {
        if (obj.orderStatus === "COMPLETE") {
          return "已完成";
        } else if (obj.orderStatus === "FAILED") {
          return "已失败";
        }
        return "待支付";
      }
      if (obj.refundStatus === "WAIT_REFUND") {
        return "待退款";
      } else if (obj.refundStatus === "REFUND") {
        return "已退款";
      } else if (obj.refundStatus === "REFUNDING") {
        return "退款中";
      }
      return "退款失败";
    },
    formatDate(str = "") {
      const data1 = str.split(" ");
      const data2 = data1[0].split("/");
      return data2[0] + "年" + data2[1] + "月" + data2[2] + "日" + data1[1];
    },
    refund(order) {
      if (order.refundStatus === "WAIT_REFUND") {
        uni.showToast({
          duration: 2000,
          icon: "error",
          title: "订单已申请退款",
        });
        return;
      }
      uni.showModal({
        title: "退款",
        content: "是否对该订单申请退款",
        success: (res) => {
          if (res.cancel) {
            return;
          }
          requestARefund({ orderno: order.orderno }).then((res) => {
            console.log(res);
            res = res.data;
            if (res.code !== 200) {
              uni.showToast({
                icon: "error",
                title: "申请失败",
                duration: 1500,
              });
              return;
            }
            uni.showToast({
              icon: "success",
              title: "申请成功",
              duration: 1500,
            });
            order.refundStatus = "WAIT_REFUND";
          });
        },
      });
    },
    copy(text) {
      // 复制功能
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: "复制成功",
            icon: "success",
          });
        },
        fail: () => {
          uni.showToast({
            title: "复制失败",
            icon: "none",
          });
        },
      });
    },

    allcommodity() {
      allcommodity().then((res) => {
        res = res.data;
        if (res.code !== 200) {
          return;
        }
        res.data.forEach((o) => {
          this.dict[o.id] = o.name;
        });
        this.dict = { ...this.dict };
      });
    },
  },
  onLoad(query) {
    // 检查代理端登录状态
    if (!isLogin() || !isProxyLogin()) {
      console.log("用户未登录或非代理端登录，跳转到统一登录页");
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
      });
      return;
    }
    const data = JSON.parse(query.data);
    this.range = data.range;
    this.type = data.type;
    this.getOrdersDetailsInfoByStatus();
    this.allcommodity();
  },
};
</script>

<style>
.my-order-card {
  background-color: white;
  padding: 20px;
  border-radius: 15px;
  margin-top: 15px;
}
</style>
