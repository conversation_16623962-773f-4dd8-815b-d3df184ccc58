import { post, get } from "@/utils/requset.js";

// 无规则获取商品列表
const getAllGoodsList = function (data) {
  return get("/selfoperatedgoods/loadAll", data);
};

// 获取商品列表
const getGoodsList = function (data) {
  return get("/selfoperatedgoods/queryPageGoods", data);
};

// 点击商品获取商品详情
const getGoodsDetail = function (data) {
  return get("/selfoperatedgoods/loadManyByPramas", data);
};

// 商品关键字查询商品
const queryGoodsByName = function (data) {
  return get("/selfoperatedgoods/queryGoodsByName", data);
};

//获取ETC自营商品订单列表
const getSelfGoodsOrderList = function (data) {
  return get("/selfoperatedorder/loadManyByPramas", data);
};

export {
  getGoodsList,
  getGoodsDetail,
  queryGoodsByName,
  getSelfGoodsOrderList,
  getAllGoodsList,
};
