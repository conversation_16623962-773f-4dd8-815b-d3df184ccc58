<template>
  <div class="chart-container">
    <canvas ref="chartCanvas"></canvas>
  </div>
</template>

<script>
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  BarController,
  Title,
  Tooltip,
  Legend,
} from "chart.js";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  BarController,
  Title,
  Tooltip,
  Legend
);

export default {
  name: "IncomeGradientBarChart",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    label: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    value: {
      handler() {
        this.updateChart();
      },
      deep: true,
    },
    label: {
      handler() {
        this.updateChart();
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.destroy();
    }
  },
  methods: {
    createGradient(ctx) {
      const gradient = ctx.createLinearGradient(0, 0, 0, 400);
      gradient.addColorStop(0, "rgba(16, 185, 129, 0.8)");
      gradient.addColorStop(1, "rgba(16, 185, 129, 0.2)");
      return gradient;
    },
    initChart() {
      const ctx = this.$refs.chartCanvas.getContext("2d");

      this.chart = new ChartJS(ctx, {
        type: "bar",
        data: {
          labels: this.label,
          datasets: [
            {
              label: "收入金额",
              data: this.value,
              backgroundColor: this.createGradient(ctx),
              borderColor: "rgb(16, 185, 129)",
              borderWidth: 1,
              borderRadius: 4,
              borderSkipped: false,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              backgroundColor: "rgba(0, 0, 0, 0.8)",
              titleColor: "#fff",
              bodyColor: "#fff",
              borderColor: "rgb(16, 185, 129)",
              borderWidth: 1,
              callbacks: {
                label: function (context) {
                  return `收入: ¥${context.parsed.y.toLocaleString()}`;
                },
              },
            },
          },
          scales: {
            x: {
              grid: {
                display: false,
              },
              ticks: {
                maxTicksLimit: 8,
                color: "#6b7280",
              },
            },
            y: {
              beginAtZero: true,
              grid: {
                color: "rgba(0, 0, 0, 0.05)",
              },
              ticks: {
                color: "#6b7280",
                callback: function (value) {
                  return "¥" + value.toLocaleString();
                },
              },
            },
          },
        },
      });
    },
    updateChart() {
      if (this.chart) {
        this.chart.data.labels = this.label;
        this.chart.data.datasets[0].data = this.value;
        this.chart.update();
      }
    },
  },
};
</script>

<style scoped>
.chart-container {
  position: relative;
  height: 200px;
  width: 100%;
}
</style>
