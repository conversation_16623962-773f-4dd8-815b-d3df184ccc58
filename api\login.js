import {
	post
} from '@/utils/requset.js'

const sendCode = function(data) {
	return post('/login/sendCode', data)
}

const checkLogin = function(data) {
	return post('/login/checkLogin', data)
}

const wxlogin = function(data) {
	return post('/login/wxlogin', data)
}

const getLoginInfo = function(data) {
	return post('/login/loadObjectByPramas', data)
}

export {
	sendCode,
	checkLogin,
	wxlogin,
	getLoginInfo
}