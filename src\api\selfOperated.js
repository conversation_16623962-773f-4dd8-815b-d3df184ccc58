import { post, get } from "@/utils/requestMethod";
import axios from "axios";
import { getToken } from "@/utils/auth";

// 分页获取自营订单列表
const getOrderList = function (data) {
  return get("/selfoperatedorder/pageByPramas", data);
};

// 自营订单条件查询
const getSelfOrderCondition = function (data) {
  return get("/selfoperatedorder/getOrderByTime", data);
};

// 获取自营订单excel数据
const getSelfOrderExcel = function (data) {
  return post("/selfoperatedorder/download", data, 5 * 60 * 1000);
};

// 更新自营订单数据
const updateSelfOrder = function (data) {
  return post("/selfoperatedorder/updT", data);
};

// 分页获取商品列表
const getGoodsList = function (data) {
  return get("/selfoperatedgoods/pageByPramas", data);
};

// 编辑商品信息
const updateGoodsInfo = function (data) {
  return post("/selfoperatedgoods/updateGoods", data);
};

// 添加商品信息
const addGoods = function (data) {
  return post("/selfoperatedgoods/addGoods", data);
};

// 上传图片返回链接
const uploadImage = function (data) {
  return post("/selfoperatedgoods/common/upload", data);
};

// 自营商品订单退款
const refund = function (data) {
  // 格式化 order_time 字段
  const formatData = { ...data };
  if (formatData.order_time) {
    formatData.order_time = formatData.order_time.replace(/\//g, "-");
  }
  return post("/fuioepay/selfOrderRefund", formatData);
};

export {
  getOrderList,
  getSelfOrderCondition,
  getSelfOrderExcel,
  getGoodsList,
  updateGoodsInfo,
  addGoods,
  uploadImage,
  refund,
  updateSelfOrder,
};
