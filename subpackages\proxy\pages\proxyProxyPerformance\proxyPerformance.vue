<template>
  <view class="container">
    <!-- 导航 -->
    <view class="nav-bar">
      <view
        class="nav-item"
        :class="{ active: checkNav === 0 }"
        @click="clickNav(0)"
      >
        <view class="nav-text">日报</view>
      </view>
      <view
        class="nav-item"
        :class="{ active: checkNav === 1 }"
        @click="clickNav(1)"
      >
        <view class="nav-text">周报</view>
      </view>
      <view
        class="nav-item"
        :class="{ active: checkNav === 2 }"
        @click="clickNav(2)"
      >
        <view class="nav-text">月报</view>
      </view>
      <view
        class="nav-item"
        :class="{ active: checkNav === 3 }"
        @click="clickNav(3)"
      >
        <view class="nav-text" @click="showDatePicker">自定义</view>
      </view>
    </view>

    <!-- 时间显示 -->
    <view class="date-info">{{ dateString }}</view>

    <!-- 代理信息 -->
    <view class="proxy-name">
      {{
        proxyInfo.relationship === "" ? proxy.userName : proxyInfo.relationship
      }}
    </view>

    <!-- 完成订单卡片 -->
    <view class="performance-card" @click="toOrderList('0')">
      <view class="card-header">
        <view class="card-title">已完成订单</view>
        <view class="arrow">›</view>
      </view>

      <view class="card-stats">
        <view class="stat-item">
          <view class="stat-label">订单数</view>
          <view class="stat-value">{{
            proxyInfo.completeCount === undefined
              ? "0"
              : proxyInfo.completeCount
          }}</view>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <view class="stat-label">金额数</view>
          <view class="stat-value amount"
            >{{
              proxyInfo.allMoney === null ? "0" : proxyInfo.allMoney.toFixed(2)
            }}￥</view
          >
        </view>
      </view>

      <!-- 商品列表 -->
      <view
        v-if="proxyInfo.goods && proxyInfo.goods.length > 0"
        class="goods-section"
      >
        <view class="goods-list">
          <view
            class="goods-item"
            v-for="(data, index) in proxyInfo.goods"
            :key="index"
          >
            {{ data.name }}: {{ data.count }}
          </view>
        </view>
      </view>
    </view>

    <!-- 退货订单卡片 -->
    <view class="performance-card" @click="toOrderList('1')">
      <view class="card-header">
        <view class="card-title">退货订单</view>
        <view class="arrow">›</view>
      </view>

      <view class="card-stats">
        <view class="stat-item">
          <view class="stat-label">退货订单</view>
          <view class="stat-value">{{ proxyInfo.FailCount }}</view>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <view class="stat-label">退款金额数</view>
          <view class="stat-value amount"
            >{{
              proxyInfo.FailMoney === null
                ? "0"
                : proxyInfo.FailMoney.toFixed(2)
            }}￥</view
          >
        </view>
      </view>

      <!-- 退款明细 -->
      <view
        v-if="proxyInfo.refundOrders && proxyInfo.refundOrders.length > 0"
        class="refund-section"
      >
        <view class="refund-list">
          <view
            class="refund-item"
            v-for="(data, index) in proxyInfo.refundOrders"
            :key="index"
          >
            <view class="refund-phone">手机号：{{ data.user.phnumber }}</view>
            <view class="refund-car">车牌号：{{ data.user.carId }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDateByDay } from "@/utils/dateUtil.js";
import { getInformationsByData } from "@/subpackages/proxy/api/proxyPerformance.js";
import { getUserId } from "@/utils/auth.js";

const getTime = function (str = "", flog) {
  if (flog === false) {
    return str.replaceAll("/", "-") + " 00:00:00";
  }
  return str.replaceAll("/", "-") + " 23:59:59";
};

const toLocaleDateString = function (data = new Date()) {
  let res = "";
  res += data.getFullYear() + "/";
  res += data.getMonth() + 1 + "/";
  res += data.getDate();
  return res;
};

export default {
  data() {
    return {
      checkNav: 0,
      range: ["", ""],
      proxy: {
        userName: "",
      },
      proxyInfo: {
        count: 0,
        FailMoney: 0,
        FailCount: 0,
        allMoney: 0,
        goods: [],
        Fail: [],
        relationship: "",
        finishOrders: [],
        refundOrders: [],
      },
    };
  },
  methods: {
    clickNav(index) {
      this.checkNav = index;
      if (index !== 3) {
        let start;
        let end = toLocaleDateString(getDateByDay(0));
        if (index === 0) {
          start = end;
        } else if (index === 1) {
          start = toLocaleDateString(getDateByDay(6));
        } else if (index === 2) {
          start = toLocaleDateString(getDateByDay(30));
        }
        this.range = [start, end];
        this.getData();
      }
    },
    getData() {
      console.log(this.proxy);
      uni.showLoading({
        title: "加载中",
      });
      const data = {
        userid: this.proxy.id,
        startTime: getTime(this.range[0], false),
        endTime: getTime(this.range[1], true),
      };
      getInformationsByData(data)
        .then((res) => {
          if (res.data.code !== 200) {
            return;
          }
          this.proxyInfo = res.data.data;
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    changeDate() {
      this.getData();
    },
    toOrderList(type) {
      if (this.proxy.id !== getUserId()) {
        return;
      }
      const data = {
        range: this.range.map((o) => {
          return o.replaceAll("/", "-");
        }),
        type,
      };
      uni.navigateTo({
        url: "/pages/proxyOrderList/orderList?data=" + JSON.stringify(data),
      });
    },
    // 显示日期选择器
    showDatePicker() {
      uni.showModal({
        title: "日期选择",
        content: "请选择日期范围（简化实现，实际项目中可使用原生picker）",
        confirmText: "确定",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            // 这里可以调用原生的日期选择器
            this.openNativeDatePicker();
          }
        },
      });
    },

    openNativeDatePicker() {
      // 使用原生日期选择器
      const date = new Date();
      const currentDate = `${date.getFullYear()}-${String(
        date.getMonth() + 1
      ).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;

      // 这里可以根据需要实现具体的日期选择逻辑
      console.log("打开原生日期选择器");
    },
  },
  onLoad(query) {
    const data = JSON.parse(query.data);
    this.proxy = data.proxy;
    const range = data.range;
    this.range = range;
    this.checkNav = data.checkNav;
    this.getData();
  },
  computed: {
    dateString() {
      let start = this.range[0];
      let end = this.range[1];
      console.log(start, end);
      start = start.replaceAll("-", "/");
      end = end.replaceAll("-", "/");
      const list1 = start.split("/");
      const list2 = end.split("/");
      let res = "";
      if (list1.length === 0) {
        return res;
      }
      res = list1[0] + "年" + list1[1] + "月" + list1[2] + "日";
      if (start === end) {
        return res;
      }
      res += " 至 " + list2[0] + "年" + list2[1] + "月" + list2[2] + "日";
      return res;
    },
  },
};
</script>

<style>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px;
}

.nav-bar {
  display: flex;
  background-color: white;
  border-radius: 8px;
  padding: 5px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-item {
  flex: 1;
  text-align: center;
  padding: 12px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.nav-item.active {
  background-color: #007aff;
  color: white;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
}

.date-info {
  text-align: center;
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
  padding: 10px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.proxy-name {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.performance-card {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.arrow {
  font-size: 20px;
  color: #ccc;
}

.card-stats {
  display: flex;
  align-items: center;
  background-color: #f8f8f8;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #999;
  margin-bottom: 5px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.stat-value.amount {
  color: #ff6b35;
}

.stat-divider {
  width: 1px;
  height: 30px;
  background-color: #e0e0e0;
  margin: 0 15px;
}

.goods-section,
.refund-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 15px;
}

.goods-list {
  display: flex;
  flex-wrap: wrap;
}

.goods-item {
  background-color: #e8f4fd;
  color: #007aff;
  padding: 6px 12px;
  margin: 4px 8px 4px 0;
  border-radius: 16px;
  font-size: 14px;
}

.refund-list {
  display: flex;
  flex-direction: column;
}

.refund-item {
  background-color: #fff5f5;
  border: 1px solid #ffe0e0;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 8px;
}

.refund-phone,
.refund-car {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.refund-car {
  margin-bottom: 0;
}
</style>
