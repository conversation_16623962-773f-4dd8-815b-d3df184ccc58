<template>
  <view class="performance-detail-container">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <view class="nav-title">个人业绩</view>
      <view class="nav-subtitle">{{ proxyInfo.relationship === "" ? proxy.userName : proxyInfo.relationship }}</view>
    </view>

    <!-- 标签导航栏 -->
    <view class="tab-navigation">
      <view class="tab-container">
        <view class="tab-item" :class="{ 'tab-active': checkNav === 0 }" @click="clickNav(0)">
          <text class="tab-text">日报</text>
          <view v-if="checkNav === 0" class="tab-indicator"></view>
        </view>
        <view class="tab-item" :class="{ 'tab-active': checkNav === 1 }" @click="clickNav(1)">
          <text class="tab-text">周报</text>
          <view v-if="checkNav === 1" class="tab-indicator"></view>
        </view>
        <view class="tab-item" :class="{ 'tab-active': checkNav === 2 }" @click="clickNav(2)">
          <text class="tab-text">月报</text>
          <view v-if="checkNav === 2" class="tab-indicator"></view>
        </view>
        <view class="tab-item" :class="{ 'tab-active': checkNav === 3 }" @click="clickNav(3)">
          <text class="tab-text" @click="showDatePicker">自定义</text>
          <view v-if="checkNav === 3" class="tab-indicator"></view>
        </view>
      </view>
    </view>

    <!-- 时间显示 -->
    <view class="date-info">{{ dateString }}</view>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <!-- 完成订单卡片 -->
      <view class="performance-card" @click="toOrderList('0')">
        <view class="card-header">
          <view class="card-title">
            <uni-icons type="checkmarkempty" size="16" color="#00d4aa"></uni-icons>
            <text class="title-text">已完成订单</text>
          </view>
          <view class="arrow">
            <uni-icons type="right" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <view class="card-stats">
          <view class="stat-item">
            <view class="stat-label">订单数</view>
            <view class="stat-value">{{
              proxyInfo.completeCount === undefined
                ? "0"
                : proxyInfo.completeCount
            }}</view>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <view class="stat-label">金额数</view>
            <view class="stat-value amount">{{
              proxyInfo.allMoney === null ? "0" : proxyInfo.allMoney.toFixed(2)
            }}￥</view>
          </view>
        </view>

        <!-- 商品列表 -->
        <view v-if="proxyInfo.goods && proxyInfo.goods.length > 0" class="goods-section">
          <view class="goods-list">
            <view class="goods-item" v-for="(data, index) in proxyInfo.goods" :key="index">
              {{ data.name }}: {{ data.count }}
            </view>
          </view>
        </view>
      </view>

      <!-- 退货订单卡片 -->
      <view class="performance-card" @click="toOrderList('1')">
        <view class="card-header">
          <view class="card-title">
            <uni-icons type="undo" size="16" color="#ff6b35"></uni-icons>
            <text class="title-text">退货订单</text>
          </view>
          <view class="arrow">
            <uni-icons type="right" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <view class="card-stats">
          <view class="stat-item">
            <view class="stat-label">退货订单</view>
            <view class="stat-value">{{ proxyInfo.FailCount }}</view>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <view class="stat-label">退款金额数</view>
            <view class="stat-value amount">{{
              proxyInfo.FailMoney === null
                ? "0"
                : proxyInfo.FailMoney.toFixed(2)
            }}￥</view>
          </view>
        </view>

        <!-- 退款明细 -->
        <view v-if="proxyInfo.refundOrders && proxyInfo.refundOrders.length > 0" class="refund-section">
          <view class="refund-list">
            <view class="refund-item" v-for="(data, index) in proxyInfo.refundOrders" :key="index">
              <view class="refund-phone">手机号：{{ data.user.phnumber }}</view>
              <view class="refund-car">车牌号：{{ data.user.carId }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getDateByDay } from "@/utils/dateUtil.js";
import { getInformationsByData } from "@/subpackages/proxy/api/proxyPerformance.js";
import { getUserId } from "@/utils/auth.js";

const getTime = function (str = "", flog) {
  if (flog === false) {
    return str.replaceAll("/", "-") + " 00:00:00";
  }
  return str.replaceAll("/", "-") + " 23:59:59";
};

const toLocaleDateString = function (data = new Date()) {
  let res = "";
  res += data.getFullYear() + "/";
  res += data.getMonth() + 1 + "/";
  res += data.getDate();
  return res;
};

export default {
  data() {
    return {
      checkNav: 0,
      range: ["", ""],
      proxy: {
        userName: "",
      },
      proxyInfo: {
        count: 0,
        FailMoney: 0,
        FailCount: 0,
        allMoney: 0,
        goods: [],
        Fail: [],
        relationship: "",
        finishOrders: [],
        refundOrders: [],
      },
    };
  },
  methods: {
    clickNav(index) {
      this.checkNav = index;
      if (index !== 3) {
        let start;
        let end = toLocaleDateString(getDateByDay(0));
        if (index === 0) {
          start = end;
        } else if (index === 1) {
          start = toLocaleDateString(getDateByDay(6));
        } else if (index === 2) {
          start = toLocaleDateString(getDateByDay(30));
        }
        this.range = [start, end];
        this.getData();
      }
    },
    getData() {
      console.log(this.proxy);
      uni.showLoading({
        title: "加载中",
      });
      const data = {
        userid: this.proxy.id,
        startTime: getTime(this.range[0], false),
        endTime: getTime(this.range[1], true),
      };
      getInformationsByData(data)
        .then((res) => {
          if (res.data.code !== 200) {
            return;
          }
          this.proxyInfo = res.data.data;
        })
        .finally(() => {
          uni.hideLoading();
        });
    },
    changeDate() {
      this.getData();
    },
    toOrderList(type) {
      if (this.proxy.id !== getUserId()) {
        return;
      }
      const data = {
        range: this.range.map((o) => {
          return o.replaceAll("/", "-");
        }),
        type,
      };
      uni.navigateTo({
        url: "/pages/proxyOrderList/orderList?data=" + JSON.stringify(data),
      });
    },
    // 显示日期选择器
    showDatePicker() {
      uni.showModal({
        title: "日期选择",
        content: "请选择日期范围（简化实现，实际项目中可使用原生picker）",
        confirmText: "确定",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            // 这里可以调用原生的日期选择器
            this.openNativeDatePicker();
          }
        },
      });
    },

    openNativeDatePicker() {
      // 使用原生日期选择器
      const date = new Date();
      const currentDate = `${date.getFullYear()}-${String(
        date.getMonth() + 1
      ).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;

      // 这里可以根据需要实现具体的日期选择逻辑
      console.log("打开原生日期选择器");
    },
  },
  onLoad(query) {
    const data = JSON.parse(query.data);
    this.proxy = data.proxy;
    const range = data.range;
    this.range = range;
    this.checkNav = data.checkNav;
    this.getData();
  },
  computed: {
    dateString() {
      let start = this.range[0];
      let end = this.range[1];
      console.log(start, end);
      start = start.replaceAll("-", "/");
      end = end.replaceAll("-", "/");
      const list1 = start.split("/");
      const list2 = end.split("/");
      let res = "";
      if (list1.length === 0) {
        return res;
      }
      res = list1[0] + "年" + list1[1] + "月" + list1[2] + "日";
      if (start === end) {
        return res;
      }
      res += " 至 " + list2[0] + "年" + list2[1] + "月" + list2[2] + "日";
      return res;
    },
  },
};
</script>

<style lang="scss">
/* 页面容器 - 统一用户端样式 */
.performance-detail-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
}

/* 顶部导航栏 - 统一用户端样式 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 标签导航栏 - 统一用户端样式 */
.tab-navigation {
  background: white;
  padding: 0 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .tab-container {
    display: flex;
    justify-content: space-around;

    .tab-item {
      position: relative;
      padding: 16px 8px;
      flex: 1;
      text-align: center;
      transition: all 0.3s ease;

      .tab-text {
        font-size: 14px;
        color: #666;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      .tab-indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 3px;
        background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
        border-radius: 2px;
        animation: slideIn 0.3s ease;
      }

      &.tab-active {
        .tab-text {
          color: #00d4aa;
          font-weight: 600;
        }
      }
    }
  }
}

@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }

  to {
    width: 30px;
    opacity: 1;
  }
}

/* 时间显示 */
.date-info {
  text-align: center;
  font-size: 16px;
  color: #666;
  margin: 20px;
  padding: 15px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* 内容区域 */
.content-wrapper {
  padding: 0 20px 20px;
}

/* 业绩卡片 - 统一用户端样式 */
.performance-card {
  background: white;
  border-radius: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }

  /* 卡片头部 */
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 16px;
    border-bottom: 1px solid #f5f5f5;

    .card-title {
      display: flex;
      align-items: center;
      flex: 1;

      .title-text {
        margin-left: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        line-height: 1.4;
      }
    }

    .arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border-radius: 12px;
      background: rgba(0, 212, 170, 0.1);
      transition: all 0.3s ease;

      &:active {
        background: rgba(0, 212, 170, 0.2);
        transform: scale(0.95);
      }
    }
  }

  /* 统计数据 */
  .card-stats {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, rgba(0, 212, 170, 0.05) 0%, rgba(0, 163, 137, 0.05) 100%);
    border-radius: 12px;
    padding: 20px;
    margin: 16px 20px;

    .stat-item {
      flex: 1;
      text-align: center;

      .stat-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        font-weight: 500;
      }

      .stat-value {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        letter-spacing: 0.3px;

        &.amount {
          color: #00d4aa;
        }
      }
    }

    .stat-divider {
      width: 1px;
      height: 40px;
      background-color: rgba(0, 212, 170, 0.2);
      margin: 0 20px;
    }
  }

  /* 商品区域 */
  .goods-section {
    border-top: 1px solid #f5f5f5;
    padding: 16px 20px 20px;

    .goods-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .goods-item {
        background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 163, 137, 0.1) 100%);
        color: #00d4aa;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 500;
        border: 1px solid rgba(0, 212, 170, 0.2);
        transition: all 0.3s ease;

        &:active {
          transform: scale(0.95);
          background: linear-gradient(135deg, rgba(0, 212, 170, 0.15) 0%, rgba(0, 163, 137, 0.15) 100%);
        }
      }
    }
  }

  /* 退款区域 */
  .refund-section {
    border-top: 1px solid #f5f5f5;
    padding: 16px 20px 20px;

    .refund-list {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .refund-item {
        background: rgba(255, 107, 53, 0.05);
        border: 1px solid rgba(255, 107, 53, 0.2);
        border-radius: 12px;
        padding: 16px;
        transition: all 0.3s ease;

        &:active {
          background: rgba(255, 107, 53, 0.1);
          transform: scale(0.98);
        }

        .refund-phone,
        .refund-car {
          font-size: 14px;
          color: #666;
          margin-bottom: 6px;
          font-weight: 500;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>
