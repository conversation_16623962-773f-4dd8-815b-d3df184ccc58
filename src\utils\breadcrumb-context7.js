/**
 * Context7 面包屑导航工具类
 * 基于Context7的设计理念，提供智能的面包屑导航功能
 */

class BreadcrumbContext7 {
  constructor(options = {}) {
    this.options = {
      // 缓存配置
      enableCache: true,
      cacheMaxAge: 5 * 60 * 1000, // 5分钟

      // 路由配置
      rootPath: "/",
      excludePaths: ["/login", "/404"],

      // 显示配置
      maxLevel: 5,
      showHome: true,
      homeTitle: "首页",

      // 自定义标题函数
      titleResolver: null,

      // 图标映射
      iconMapping: {
        home: "home",
        dashboard: "dashboard",
        user: "user",
        setting: "setting",
      },

      ...options,
    };

    this.cache = new Map();
    this.listeners = new Set();

    // 初始化
    this.init();
  }

  /**
   * 初始化
   */
  init() {
    // 清理过期缓存的定时器
    if (this.options.enableCache) {
      setInterval(() => {
        this.cleanExpiredCache();
      }, this.options.cacheMaxAge);
    }
  }

  /**
   * 生成面包屑数据
   * @param {Object} route - Vue路由对象
   * @param {Object} routeConfig - 路由配置
   * @returns {Array} 面包屑数组
   */
  generateBreadcrumb(route, routeConfig = {}) {
    try {
      const cacheKey = this.getCacheKey(route);

      // 检查缓存
      if (this.options.enableCache && this.cache.has(cacheKey)) {
        const cached = this.cache.get(cacheKey);
        if (Date.now() - cached.timestamp < this.options.cacheMaxAge) {
          return cached.data;
        }
      }

      // 生成面包屑
      const breadcrumb = this.buildBreadcrumb(route, routeConfig);

      // 缓存结果
      if (this.options.enableCache) {
        this.cache.set(cacheKey, {
          data: breadcrumb,
          timestamp: Date.now(),
        });
      }

      // 触发监听器
      this.notifyListeners(breadcrumb, route);

      return breadcrumb;
    } catch (error) {
      console.error("生成面包屑失败:", error);
      return [];
    }
  }

  /**
   * 构建面包屑数据
   * @param {Object} route - Vue路由对象
   * @param {Object} routeConfig - 路由配置
   * @returns {Array} 面包屑数组
   */
  buildBreadcrumb(route, routeConfig) {
    // 过滤有效的路由匹配
    let matched = route.matched.filter((item) => {
      return (
        item.meta &&
        item.meta.title &&
        item.meta.breadcrumb !== false &&
        !this.options.excludePaths.includes(item.path)
      );
    });

    // 应用层级限制
    if (this.options.maxLevel && matched.length > this.options.maxLevel) {
      matched = matched.slice(-this.options.maxLevel);
    }

    // 构建面包屑项
    const breadcrumb = matched.map((routeItem, index) => {
      return this.createBreadcrumbItem(routeItem, route, index, matched.length);
    });

    // 添加首页（只有当第一个项目不是首页时才添加）
    if (this.options.showHome && !this.hasHomePage(breadcrumb)) {
      breadcrumb.unshift(this.createHomeItem());
    }

    // 标记最后一项为当前页面（不可点击）
    if (breadcrumb.length > 0) {
      const lastItem = breadcrumb[breadcrumb.length - 1];
      lastItem.isLast = true;
      lastItem.isCurrent = true;
      lastItem.clickable = false;
    }

    return breadcrumb;
  }

  /**
   * 创建面包屑项
   * @param {Object} routeItem - 路由项
   * @param {Object} currentRoute - 当前路由
   * @param {Number} index - 索引
   * @param {Number} total - 总数
   * @returns {Object} 面包屑项
   */
  createBreadcrumbItem(routeItem, currentRoute, index, total) {
    const item = {
      path: this.compilePath(routeItem.path, currentRoute.params),
      name: routeItem.name,
      meta: { ...routeItem.meta },
      isLast: index === total - 1,
      isCurrent: routeItem.path === currentRoute.path,
      level: index + 1,
    };

    // 解析标题
    item.title = this.resolveTitle(item, currentRoute);

    // 解析图标
    item.icon = this.resolveIcon(item);

    // 添加额外的元数据
    item.timestamp = Date.now();
    item.id = this.generateItemId(item);

    return item;
  }

  /**
   * 创建首页项
   * @returns {Object} 首页面包屑项
   */
  createHomeItem() {
    return {
      path: this.options.rootPath,
      name: "Home",
      title: this.options.homeTitle,
      icon: this.options.iconMapping.home,
      meta: {
        title: this.options.homeTitle,
        icon: this.options.iconMapping.home,
        isHome: true,
      },
      isHome: true,
      isLast: false,
      isCurrent: false,
      level: 0,
      timestamp: Date.now(),
      id: "home",
    };
  }

  /**
   * 解析标题
   * @param {Object} item - 面包屑项
   * @param {Object} route - 当前路由
   * @returns {String} 标题
   */
  resolveTitle(item, route) {
    // 自定义标题解析器
    if (
      this.options.titleResolver &&
      typeof this.options.titleResolver === "function"
    ) {
      const customTitle = this.options.titleResolver(item, route);
      if (customTitle) return customTitle;
    }

    // 动态参数替换
    let title = item.meta.title || item.name;

    if (route.params && typeof title === "string") {
      Object.keys(route.params).forEach((key) => {
        title = title.replace(
          new RegExp(`{{\\s*${key}\\s*}}`, "g"),
          route.params[key]
        );
      });
    }

    return title;
  }

  /**
   * 解析图标
   * @param {Object} item - 面包屑项
   * @returns {String} 图标名称
   */
  resolveIcon(item) {
    if (item.meta.icon) {
      return item.meta.icon;
    }

    // 根据路径推断图标
    const pathSegments = item.path.split("/").filter(Boolean);
    for (const segment of pathSegments) {
      if (this.options.iconMapping[segment]) {
        return this.options.iconMapping[segment];
      }
    }

    return null;
  }

  /**
   * 编译路径（处理动态参数）
   * @param {String} path - 路径模板
   * @param {Object} params - 路由参数
   * @returns {String} 编译后的路径
   */
  compilePath(path, params = {}) {
    if (!params || Object.keys(params).length === 0) {
      return path;
    }

    return path.replace(/:(\w+)/g, (match, key) => {
      return params[key] || match;
    });
  }

  /**
   * 检查是否包含首页
   * @param {Array} breadcrumb - 面包屑数组
   * @returns {Boolean} 是否包含首页
   */
  hasHomePage(breadcrumb) {
    return breadcrumb.some(
      (item) =>
        item.path === this.options.rootPath || item.meta?.isHome || item.isHome
    );
  }

  /**
   * 生成缓存键
   * @param {Object} route - 路由对象
   * @returns {String} 缓存键
   */
  getCacheKey(route) {
    const pathKey = route.matched.map((item) => item.path).join("|");
    const paramKey = JSON.stringify(route.params || {});
    return `${pathKey}#${paramKey}`;
  }

  /**
   * 生成项目ID
   * @param {Object} item - 面包屑项
   * @returns {String} 项目ID
   */
  generateItemId(item) {
    return `breadcrumb-${item.name || item.path.replace(/[^\w]/g, "-")}-${
      item.level
    }`;
  }

  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.options.cacheMaxAge) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * 添加监听器
   * @param {Function} listener - 监听函数
   */
  addListener(listener) {
    if (typeof listener === "function") {
      this.listeners.add(listener);
    }
  }

  /**
   * 移除监听器
   * @param {Function} listener - 监听函数
   */
  removeListener(listener) {
    this.listeners.delete(listener);
  }

  /**
   * 通知监听器
   * @param {Array} breadcrumb - 面包屑数据
   * @param {Object} route - 路由对象
   */
  notifyListeners(breadcrumb, route) {
    this.listeners.forEach((listener) => {
      try {
        listener(breadcrumb, route);
      } catch (error) {
        console.error("面包屑监听器执行失败:", error);
      }
    });
  }

  /**
   * 获取面包屑分析
   * @param {Array} breadcrumb - 面包屑数组
   * @returns {Object} 分析结果
   */
  analyzeBreadcrumb(breadcrumb) {
    return {
      depth: breadcrumb.length,
      hasHome: this.hasHomePage(breadcrumb),
      currentPage: breadcrumb.find((item) => item.isCurrent),
      lastPage: breadcrumb[breadcrumb.length - 1],
      paths: breadcrumb.map((item) => item.path),
      titles: breadcrumb.map((item) => item.title),
    };
  }

  /**
   * 导出配置
   * @returns {Object} 当前配置
   */
  exportConfig() {
    return { ...this.options };
  }

  /**
   * 更新配置
   * @param {Object} newOptions - 新配置
   */
  updateConfig(newOptions) {
    this.options = { ...this.options, ...newOptions };

    // 清空缓存（配置变更可能影响结果）
    this.cache.clear();
  }

  /**
   * 重置
   */
  reset() {
    this.cache.clear();
    this.listeners.clear();
  }

  /**
   * 销毁
   */
  destroy() {
    this.reset();
    this.options = null;
  }
}

// 创建单例实例
let instance = null;

/**
 * 获取Context7面包屑实例
 * @param {Object} options - 配置选项
 * @returns {BreadcrumbContext7} 实例
 */
export function getBreadcrumbContext7(options = {}) {
  if (!instance) {
    instance = new BreadcrumbContext7(options);
  }
  return instance;
}

/**
 * 创建新的Context7面包屑实例
 * @param {Object} options - 配置选项
 * @returns {BreadcrumbContext7} 新实例
 */
export function createBreadcrumbContext7(options = {}) {
  return new BreadcrumbContext7(options);
}

// Vue插件形式
export const BreadcrumbContext7Plugin = {
  install(Vue, options = {}) {
    const breadcrumbContext7 = getBreadcrumbContext7(options);

    // 全局属性
    Vue.prototype.$breadcrumbContext7 = breadcrumbContext7;

    // 全局混入
    Vue.mixin({
      created() {
        // 自动注册路由变化监听
        if (this.$router && this.$route) {
          this.$watch(
            "$route",
            (newRoute) => {
              if (
                this.onBreadcrumbChange &&
                typeof this.onBreadcrumbChange === "function"
              ) {
                const breadcrumb =
                  breadcrumbContext7.generateBreadcrumb(newRoute);
                this.onBreadcrumbChange(breadcrumb, newRoute);
              }
            },
            { immediate: true }
          );
        }
      },
    });
  },
};

export default BreadcrumbContext7;
