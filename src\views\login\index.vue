﻿<template>
  <div class="login-container">
    <div class="login-background">
      <div class="background-pattern" />
      <div class="floating-shapes">
        <div class="shape shape-1" />
        <div class="shape shape-2" />
        <div class="shape shape-3" />
        <div class="shape shape-4" />
      </div>
    </div>

    <div class="login-content">
      <div class="login-card">
        <div class="login-header">
          <div class="logo-section">
            <div class="logo-icon">
              <i class="el-icon-s-platform" />
            </div>
            <h1 class="title">ETC伴侣管理端</h1>
            <p class="subtitle">欢迎回来，请登录您的账户</p>
          </div>
        </div>

        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          auto-complete="on"
          label-position="left"
        >
          <div class="form-group">
            <label class="form-label">手机号码</label>
            <el-form-item prop="username">
              <el-input
                ref="username"
                v-model="loginForm.username"
                placeholder="请输入手机号码"
                name="username"
                type="text"
                tabindex="1"
                auto-complete="on"
                maxlength="11"
                class="modern-input"
              >
                <template slot="prefix">
                  <div class="flex items-center justify-center w-8 h-full">
                    <svg-icon icon-class="user" />
                  </div>
                </template>
              </el-input>
            </el-form-item>
          </div>

          <div class="form-group">
            <label class="form-label">密码</label>
            <el-form-item prop="password">
              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.password"
                :type="passwordType"
                placeholder="请输入验证码"
                name="password"
                tabindex="2"
                auto-complete="on"
                class="modern-input"
                @keyup.enter.native="handleLogin"
              >
                <template slot="prefix">
                  <div class="flex items-center justify-center w-8 h-full">
                    <svg-icon icon-class="password" />
                  </div>
                </template>
                <template slot="suffix">
                  <div class="password-toggle" @click="showPwd">
                    <svg-icon
                      :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'"
                      class="password-toggle-icon"
                    />
                  </div>
                </template>
              </el-input>
            </el-form-item>
          </div>

          <div class="login-actions">
            <el-button
              :loading="loading"
              type="primary"
              class="login-btn"
              @click.native.prevent="handleLogin"
            >
              <span v-if="!loading">立即登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { wxSendCode, login } from "@/api/login";
import { setToken, setUserId } from "@/utils/auth";
import { handleError, handleBusinessError } from "@/utils/errorHandler";

let it;

export default {
  name: "Login",
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入手机号码"));
      } else if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error("请输入正确的手机号码"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入验证码"));
      } else if (value.length < 6) {
        callback(new Error("请输入正确的验证码"));
      } else {
        callback();
      }
    };
    return {
      ms: 0,
      loginForm: {
        username: "",
        password: "",
      },
      loginRules: {
        username: [
          {
            required: true,
            trigger: "blur",
            validator: validateUsername,
          },
        ],
        password: [
          {
            required: true,
            trigger: "blur",
            validator: validatePassword,
          },
        ],
      },
      loading: false,
      passwordType: "password",
      redirect: undefined,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true,
    },
  },
  methods: {
    // 发送验证码
    wxSendCode() {
      if (this.loginForm.username.length !== 11) {
        this.$message.error("手机号码有误");
        return;
      }
      this.ms = 60;
      it = setInterval(() => {
        this.ms = this.ms - 1;
        if (this.ms <= 0) {
          clearInterval(it);
          this.ms = 0;
        }
      }, 1000);
      const data = {
        phone: this.loginForm.username,
        adminRabk: "3",
      };
      wxSendCode(data)
        .then((res) => {
          console.log(res);
          if (handleBusinessError(res, "验证码发送失败")) {
            this.$message.success("验证码发送成功");
          } else {
            this.ms = 0;
            clearInterval(it);
          }
        })
        .catch((error) => {
          handleError(error, "验证码发送失败");
          this.ms = 0;
          clearInterval(it);
        });
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "text";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    handleLogin() {
      // 表单验证
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          const data = {
            adminRabk: "3",
            phone: this.loginForm.username,
            verificationCode: this.loginForm.password,
          };
          login(data)
            .then((res) => {
              console.log(res);
              if (handleBusinessError(res, "登录失败")) {
                const resData = res.data;
                setToken(resData.data.token);
                setUserId(resData.data.userId);
                this.$message.success("登录成功");
                this.$router.push({
                  path: this.redirect || "/",
                });
              }
            })
            .catch((error) => {
              handleError(error, "登录失败，请稍后重试");
            })
            .finally(() => {
              this.loading = false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
  z-index: 0;

  .background-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    background-image: radial-gradient(
        circle at 25% 25%,
        white 2px,
        transparent 2px
      ),
      radial-gradient(circle at 75% 75%, white 2px, transparent 2px);
    background-size: 100px 100px;
    background-position: 0 0, 50px 50px;
  }

  .floating-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;

      &.shape-1 {
        width: 80px;
        height: 80px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.shape-2 {
        width: 120px;
        height: 120px;
        top: 20%;
        right: 15%;
        animation-delay: 2s;
      }

      &.shape-3 {
        width: 60px;
        height: 60px;
        bottom: 30%;
        left: 20%;
        animation-delay: 4s;
      }

      &.shape-4 {
        width: 100px;
        height: 100px;
        bottom: 10%;
        right: 10%;
        animation-delay: 1s;
      }
    }
  }
}

.login-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 450px;
  padding: 6px;
}

.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
  text-align: center;
  margin-bottom: 12px;

  .logo-section {
    .logo-icon {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
      box-shadow: 0 10px 30px rgba(64, 158, 255, 0.3);

      i {
        font-size: 36px;
        color: white;
      }
    }

    .title {
      font-size: 28px;
      font-weight: 700;
      margin: 0 0 8px 0;
      background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .subtitle {
      color: #6b7280;
      font-size: 1rem;
      margin: 0;
      font-weight: 400;
    }
  }
}

.login-form {
  .form-group {
    margin-bottom: 8px;

    .form-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #374151;
      font-size: 0.8rem;
    }

    // 移除不必要的input-wrapper样式，直接使用Element UI的样式
  }

  .login-actions {
    margin-top: 32px;

    .login-btn {
      width: 100%;
      height: 54px;
      background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
      border: none;
      border-radius: 12px;
      color: white;
      font-size: 1rem;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(64, 158, 255, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      &.is-loading {
        transform: none;
      }
    }
  }
}

// Element UI 样式覆盖
:deep(.el-form-item) {
  margin-bottom: 0;

  .el-form-item__content {
    line-height: normal;
  }

  .el-form-item__error {
    padding-top: 4px;
    font-size: 12px;
  }
}

:deep(.modern-input) {
  .el-input__inner {
    height: 54px;
    line-height: 54px;
    padding: 0 15px;
    font-size: 1rem;
    color: #374151;
    background: #f8fafc;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    transition: all 0.3s ease;

    &::placeholder {
      color: #9ca3af;
    }

    &:focus {
      border-color: #409eff;
      background: white;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
    }
  }

  .el-input__prefix {
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    height: 100%;
    color: #9ca3af;
    font-size: 1.1rem;
  }

  .el-input__prefix .el-input__icon {
    width: auto;
    line-height: 1;
  }

  .el-input__suffix {
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    height: 100%;
  }
}

.password-toggle {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(64, 158, 255, 0.1);
  }

  .password-toggle-icon {
    font-size: 16px;
    color: #9ca3af;
    transition: color 0.2s ease;
  }

  &:hover .password-toggle-icon {
    color: #409eff;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-content {
    max-width: 90%;
    padding: 4px;
  }

  .login-card {
    padding: 30px 25px;
    border-radius: 8px;
  }

  .login-header {
    margin-bottom: 10px;

    .logo-section {
      .logo-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 15px;

        i {
          font-size: 28px;
        }
      }

      .title {
        font-size: 24px;
      }

      .subtitle {
        font-size: 0.8rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: 25px 20px;
  }

  .login-header .logo-section {
    .title {
      font-size: 20px;
    }

    .subtitle {
      font-size: 13px;
    }
  }
}
</style>
