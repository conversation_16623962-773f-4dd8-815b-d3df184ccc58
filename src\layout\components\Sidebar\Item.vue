<template>
  <div class="menu-item-wrapper flex items-center w-full">
    <!-- 图标 -->
    <div
      v-if="icon"
      class="menu-icon-wrapper flex items-center justify-center w-5 h-5 mr-3 flex-shrink-0"
    >
      <!-- SVG图标 -->
      <svg-icon
        v-if="isExternal"
        :icon-class="icon"
        class="w-4 h-4 text-inherit transition-all duration-200"
      />
      <!-- Element UI图标 -->
      <i
        v-else-if="icon.includes('el-icon')"
        :class="[icon, 'text-lg text-inherit transition-all duration-200']"
      ></i>
      <!-- 自定义图标 -->
      <i
        v-else
        :class="[icon, 'text-lg text-inherit transition-all duration-200']"
      ></i>
    </div>

    <!-- 标题文本 -->
    <span
      v-if="title"
      class="menu-title text-sm font-medium text-inherit leading-relaxed flex-1 whitespace-nowrap overflow-hidden text-ellipsis"
      >{{ title }}</span
    >
  </div>
</template>

<script>
import { isExternal } from "@/utils/validate";

export default {
  name: "MenuItem",
  props: {
    icon: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "",
    },
  },
  computed: {
    isExternal() {
      return isExternal(this.icon);
    },
  },
};
</script>
