import { get, post } from "@/utils/requestMethod";

// 获取小程序菜单按钮列表
const getCornList = function (data) {
  return get("corn/list", data);
};

// 更新小程序菜单按钮
const updateCorn = function (data) {
  return post("corn/update", data);
};

// 新增小程序菜单按钮
const saveCorn = function (data) {
  return post("corn/save", data);
};

// 删除小程序菜单按钮
const deleteCorn = function (id) {
  return get("corn/delete", { id });
};

export { getCornList, updateCorn, saveCorn, deleteCorn };
