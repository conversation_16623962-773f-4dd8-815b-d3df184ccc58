import { post, BASE_URL } from "@/utils/requset.js";
import { getToken } from "@/utils/auth.js";

const sendCode = function (data) {
  return post("/login/sendCode", data);
};

const checkLogin = function (data) {
  return post("/login/checkAgentLogin", data);
};

const showInformations = function () {
  return post("/userinformation/showInformations");
};

const setUserName = function (data) {
  return post("/userinformation/updInfo", data);
};

// 上传图片接口
const uploadImage = function (filePath) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: BASE_URL + "/selfoperatedgoods/common/upload",
      filePath: filePath,
      name: "file",
      header: {
        token: getToken(),
      },
      success: (res) => {
        const data = JSON.parse(res.data);
        resolve({ data });
      },
      fail: (err) => {
        reject(err);
      },
    });
  });
};

export { sendCode, checkLogin, showInformations, setUserName, uploadImage };
