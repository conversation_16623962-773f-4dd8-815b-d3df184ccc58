<template>
  <div
    class="app-wrapper h-screen w-screen flex overflow-hidden bg-gradient-to-br from-slate-50 via-slate-200 to-slate-400"
  >
    <!-- Mobile overlay -->
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="fixed inset-0 bg-black/60 backdrop-blur-sm z-[999] transition-all duration-300 ease-out"
      @click="handleClickOutside"
    />

    <!-- Sidebar container -->
    <div :class="sidebarClasses">
      <sidebar />
    </div>

    <!-- Main content container -->
    <div class="main-container flex-1 flex flex-col min-w-0 overflow-hidden">
      <!-- Navbar wrapper -->
      <div
        class="navbar-wrapper sticky top-0 z-[1000] flex-shrink-0 bg-white/90 backdrop-blur-md border-b border-white/20 mt-2 mr-2 rounded-t-xl shadow-sm shadow-black/10"
      >
        <navbar />
      </div>

      <!-- App main wrapper -->
      <div class="app-main-wrapper flex-1 overflow-auto mr-2 mb-2 rounded-b-xl">
        <app-main />
      </div>
    </div>
  </div>
</template>

<script>
import { Navbar, Sidebar, AppMain } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";

export default {
  name: "Layout",
  components: {
    Navbar,
    Sidebar,
    AppMain,
  },
  mixins: [ResizeMixin],
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar;
    },
    device() {
      return this.$store.state.app.device;
    },
    isMobile() {
      return this.device === "mobile";
    },
    isCollapsed() {
      return !this.sidebar.opened;
    },
    hasAnimation() {
      return !this.sidebar.withoutAnimation;
    },
    // 侧边栏容器样式
    sidebarClasses() {
      if (this.isMobile) {
        return [
          "fixed top-0 left-0 bottom-0 z-[1001] h-full overflow-hidden p-2 pr-0",
          "w-60 transition-transform duration-300 ease-out",
          this.sidebar.opened ? "translate-x-0" : "-translate-x-60",
        ];
      }

      return [
        "flex-shrink-0 h-full overflow-hidden p-2 pr-0",
        this.isCollapsed ? "w-[54px]" : "w-60",
        this.hasAnimation ? "transition-all duration-300 ease-out" : "",
      ];
    },
  },
  methods: {
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
  },
};
</script>
