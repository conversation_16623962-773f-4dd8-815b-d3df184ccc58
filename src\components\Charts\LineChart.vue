<template>
  <div :class="[className, 'w-full h-96']" />
</template>

<script>
import echarts from "echarts";
require("echarts/theme/macarons"); // echarts theme
import resize from "./mixins/resize";

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "350px",
    },
    autoResize: {
      type: Boolean,
      default: true,
    },
    chartData: {
      type: Object,
      required: false,
    },
    label: {
      type: Array,
      default: () => [],
    },
    echartData: {
      type: Array,
      default: () => [],
    },
    text: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      },
    },
    label: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      },
    },
    echartData: {
      deep: true,
      handler(val) {
        this.setOptions(val);
      },
    },
  },
  computed: {
    dynamicHeight() {
      return this.height || "350px";
    },
    dynamicWidth() {
      return this.width || "100%";
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");
      this.setOptions();
    },
    setOptions() {
      this.chart.setOption({
        xAxis: {
          data: this.label,
          boundaryGap: false,
          axisTick: {
            show: false,
          },
          axisLabel: {
            interval: "auto",
            rotate: 45,
            fontSize: 10,
            margin: 8,
          },
        },
        grid: {
          left: 10,
          right: 10,
          bottom: 50,
          top: 30,
          containLabel: true,
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
          },
          padding: [5, 10],
        },
        yAxis: {
          axisTick: {
            show: false,
          },
        },
        legend: {
          data: [],
        },
        series: [
          {
            name: this.text,
            itemStyle: {
              normal: {
                color: "#FF005A",
                lineStyle: {
                  color: "#FF005A",
                  width: 2,
                },
              },
            },
            smooth: true,
            type: "line",
            data: this.echartData,
            animationDuration: 2800,
            animationEasing: "cubicInOut",
          },
        ],
      });
    },
  },
};
</script>
