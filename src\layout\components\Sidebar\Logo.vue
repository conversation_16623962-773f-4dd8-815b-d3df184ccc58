<template>
  <div
    class="relative w-full mx-3 my-3 rounded-2xl overflow-hidden transition-all duration-500 ease-out"
    :class="collapse ? 'h-16' : 'h-20 '"
  >
    <div
      class="relative w-full h-full bg-gradient-to-br from-indigo-500/15 via-green-500/10 via-50% to-red-500/8 backdrop-blur-2xl backdrop-saturate-[180%] rounded-2xl border border-white/30 shadow-[0_8px_32px_rgba(0,0,0,0.08),inset_0_1px_0_rgba(255,255,255,0.2)] overflow-hidden transition-all duration-300 hover:-translate-y-0.5"
      :class="collapse ? 'rounded-xl' : 'rounded-2xl'"
    >
      <!-- 动态背景波浪 -->
      <div class="absolute inset-0 overflow-hidden">
        <div
          class="absolute w-32 h-32 -top-16 -left-16 rounded-full opacity-60 animate-spin-slow bg-wave-1"
        ></div>
        <div
          class="absolute w-20 h-20 -top-10 -right-10 rounded-full opacity-60 animate-spin-reverse-slow animation-delay-2000 bg-wave-2"
        ></div>
        <div
          class="absolute w-16 h-16 -bottom-8 left-1/2 transform -translate-x-1/2 rounded-full opacity-60 animate-spin-slow animation-delay-4000 bg-wave-3"
        ></div>
      </div>

      <!-- Logo链接容器 -->
      <router-link
        to="/"
        class="relative flex items-center justify-start w-full h-full text-decoration-none transition-all duration-300 hover:-translate-y-0.5 z-10"
        :class="collapse ? 'justify-center p-3' : 'p-4'"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
      >
        <!-- Logo图标容器 -->
        <div
          class="relative flex items-center justify-center transition-all duration-300 hover:scale-110"
          :class="collapse ? 'w-10 h-10' : 'w-12 h-12 mr-3'"
        >
          <div
            class="relative flex items-center justify-center w-full h-full bg-gradient-to-br from-white/90 to-gray-50/80 shadow-[0_4px_12px_rgba(0,0,0,0.1),inset_0_1px_0_rgba(255,255,255,0.3)] overflow-hidden transition-all duration-300"
            :class="collapse ? 'rounded-xl' : 'rounded-2xl'"
          >
            <img
              v-if="logo"
              :src="logo"
              alt="ETC Logo"
              class="object-contain rounded-lg transition-all duration-300"
              :class="collapse ? 'w-6 h-6' : 'w-8 h-8'"
            />
            <i
              v-else
              class="el-icon-s-platform bg-gradient-to-r from-indigo-500 to-purple-600 bg-clip-text text-transparent"
              :class="collapse ? 'text-xl' : 'text-3xl'"
            ></i>
          </div>

          <!-- 装饰环 -->
          <div
            class="absolute border-2 border-transparent bg-gradient-to-r from-indigo-500/60 via-green-500/40 via-50% to-red-500/30 opacity-0 transition-all duration-300 animate-spin-slow group-hover:opacity-100 group-hover:scale-110"
            :class="collapse ? '-inset-1 rounded-2xl' : '-inset-1 rounded-2xl'"
            style="background-clip: padding-box"
          ></div>

          <!-- 发光效果 -->
          <div
            class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full opacity-0 transition-all duration-300 blur-sm group-hover:opacity-100 group-hover:scale-125"
            :class="collapse ? 'w-12 h-12' : 'w-16 h-16'"
            style="
              background: radial-gradient(
                circle,
                rgba(102, 126, 234, 0.3) 0%,
                transparent 70%
              );
            "
          ></div>
        </div>

        <!-- Logo文本 -->
        <div v-if="!collapse" class="flex-1 flex flex-col justify-center">
          <h1
            class="m-0 text-lg font-bold leading-tight text-gray-800 font-sf-pro tracking-tight transition-all duration-300 bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent hover:from-indigo-500 hover:via-purple-600 hover:to-pink-500"
          >
            {{ title }}
          </h1>
          <p
            class="mt-0.5 text-[11px] font-medium text-gray-400 tracking-wide opacity-80"
          >
            管理控制台
          </p>
        </div>

        <!-- 状态指示器 -->
        <div v-if="!collapse" class="absolute top-3 right-3">
          <div
            class="w-2 h-2 rounded-full bg-gradient-to-r from-green-500 to-green-600 shadow-[0_0_0_4px_rgba(16,185,129,0.2)] transition-all duration-300 animate-pulse hover:scale-110 hover:shadow-[0_0_0_8px_rgba(16,185,129,0.3)]"
          ></div>
        </div>
      </router-link>

      <!-- 几何装饰 -->
      <div
        class="absolute inset-0 pointer-events-none"
        :class="{ hidden: collapse }"
      >
        <div
          class="absolute w-5 h-5 top-2 left-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded transform rotate-45 opacity-30 animate-float"
        ></div>
        <div
          class="absolute w-3 h-3 bottom-2 right-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full opacity-30 animate-float-reverse animation-delay-1000"
        ></div>
        <div
          class="absolute w-4 h-4 top-6 right-2 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-sm transform rotate-30 opacity-30 animate-float animation-delay-2000"
        ></div>
      </div>
    </div>

    <!-- 底部分隔线 -->
    <div
      class="absolute bottom-0 left-4 right-4 h-px bg-gradient-to-r from-transparent via-indigo-500/30 to-transparent"
    ></div>
  </div>
</template>

<script>
export default {
  name: "ModernSidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      title: "ETC管理系统",
      logo: null,
      isHovered: false,
    };
  },
  methods: {
    handleMouseEnter() {
      this.isHovered = true;
    },
    handleMouseLeave() {
      this.isHovered = false;
    },
  },
};
</script>

<style>
/* 自定义动画 - Tailwind无法直接表达的复杂动画 */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes spin-reverse-slow {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(var(--rotation, 0deg));
  }
  50% {
    transform: translateY(-4px) rotate(var(--rotation, 0deg));
  }
}

@keyframes float-reverse {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(4px);
  }
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

.animate-spin-reverse-slow {
  animation: spin-reverse-slow 8s linear infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
  --rotation: 45deg;
}

.animate-float-reverse {
  animation: float-reverse 4s ease-in-out infinite;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:scale-120 {
  transform: scale(1.2);
}

.group:hover .group-hover\:scale-130 {
  transform: translate(-50%, -50%) scale(1.3);
}

/* 自定义背景渐变 */
.bg-wave-1 {
  background: radial-gradient(
    circle,
    rgba(102, 126, 234, 0.2) 0%,
    transparent 70%
  );
}

.bg-wave-2 {
  background: radial-gradient(
    circle,
    rgba(16, 185, 129, 0.15) 0%,
    transparent 70%
  );
}

.bg-wave-3 {
  background: radial-gradient(
    circle,
    rgba(239, 68, 68, 0.1) 0%,
    transparent 70%
  );
}
</style>
