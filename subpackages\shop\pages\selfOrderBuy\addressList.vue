<template>
  <view class="address-page">
    <!-- 地址列表 -->
    <scroll-view v-if="addressList.length > 0" class="address-list" scroll-y>
      <!-- <view v-for="(item, index) in addressList" :key="index" class="address-item"
				@click="selectAddress(item.id)">
				<view class="address-content">
					<view class="radio-container">
						<radio :value="item.id" :checked="selectedAddress === item.id" />
					</view>
					<view class="address-info">
						<view class="user-info">
							<text class="name">{{item.name}}</text>
							<text class="phone">{{item.phone}}</text>
						</view>
						<text class="address">{{item.address}}</text>
						<view class="operation">
							<uni-icons type="compose" size="20" color="#666666" @click="handleEdit(item)" />
						</view>
					</view>
				</view> -->
      <!-- 左滑删除区域 -->
      <view class="swipe-container">
        <view
          v-for="(item, index) in addressList"
          :key="index"
          class="address-item"
          @click="selectAddress(item.addressId)"
        >
          <view
            class="swipe-item"
            @touchstart="onTouchStart"
            @touchmove="onTouchMove"
            @touchend="onTouchEnd"
          >
            <view class="address-content">
              <view class="radio-container">
                <radio
                  :value="item.addressId"
                  :checked="selectedAddress === item.addressId"
                />
              </view>
              <view class="address-info">
                <view class="user-info">
                  <text class="name">{{ item.recipientName }}</text>
                  <text class="phone">{{ item.phoneNumber }}</text>
                </view>
                <text class="address"
                  >{{ item.provinceCityDistrict }}{{ item.addressDetail }}</text
                >
                <view class="operation">
                  <text class="custom-icon edit-icon" @click="handleEdit(item)"
                    >✏️</text
                  >
                </view>
              </view>
            </view>
            <view class="delete-btn" @click.stop="handleDelete(item)">
              <text class="delete-text">删除</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <!-- <image class="empty-icon" :src="emptyIconUrl" mode="aspectFit" /> -->
      <text class="empty-text">暂无收件地址</text>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <button class="add-btn" @click="handleAdd">新增收件地址</button>
      <button
        v-if="addressList.length > 0"
        class="confirm-btn"
        @click="handleConfirm"
      >
        确认选择
      </button>
    </view>
  </view>
</template>

<script>
import {  getUserId } from "@/utils/auth.js";

import { queryAllAddr, updAddr } from "@/subpackages/shop/api/selfGoods.js";
import { showInformations } from "@/api/user.js";
export default {
  data() {
    return {
      emptyIconUrl:
        "https://ai-public.mastergo.com/ai/img_res/7ef021dc91eace78c4d67fd4db2c6934.jpg",
      addressList: [],
      selectedAddress: null,
      // 滑动删除相关状态
      touchStartX: 0,
      touchStartY: 0,
      swipeState: {}, // 记录每个item的滑动状态
      userInfo: null, // 添加用户信息字段
    };
  },
  methods: {
    // 获取用户信息的通用方法
    async getUserInfo() {
      if (!this.userInfo) {
        const userRes = await showInformations();
        if (userRes.data && userRes.data.code === 200) {
          this.userInfo = userRes.data.data;
          return this.userInfo;
        } else {
          throw new Error("获取用户信息失败");
        }
      }
      return this.userInfo;
    },
    goBack() {
      uni.navigateBack();
    },
    selectAddress(id) {
      console.log("点击");
      this.selectedAddress = id;
    },
    handleEdit(item) {
      uni.navigateTo({
        url:
          "/subpackages/shop/pages/selfOrderBuy/editAddress?type=upd&item=" +
          JSON.stringify(item),
      });
    },
    handleDelete(item) {
      console.log(item);
      uni.showModal({
        title: "提示",
        content: "确定要删除该地址吗？",
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: "删除中",
            });
            let data = {
              addressId: item.addressId,
              logicalDel: 1,
            };
            updAddr(data)
              .then((res) => {
                if (res.data == "updSuccess") {
                  uni.showToast({
                    title: "删除成功",
                    icon: "success",
                  });
                  this.getAdrInfo();
                } else {
                  uni.showToast({
                    title: "删除失败",
                    icon: "error",
                  });
                }
              })
              .catch((err) => {
                uni.showToast({
                  title: "错误",
                  icon: "error",
                });
              });
          }
        },
      });
    },
    handleAdd() {
      uni.navigateTo({
        url: "/subpackages/shop/pages/selfOrderBuy/editAddress",
      });
    },
    handleConfirm() {
      if (!this.selectedAddress) {
        uni.showToast({
          title: "请选择地址",
          icon: "none",
        });
        return;
      }
      const selected = this.addressList.find(
        (item) => item.addressId === this.selectedAddress
      );
      // 返回上级页面并携带数据
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2]; // 上一页实例
      console.log(prevPage);
      // 调用上一页的方法设置地址
      prevPage.$vm.setSelectedAddress(selected);

      uni.navigateBack();

      // 这里可以添加其他确认后的逻辑，比如返回上一页并带回选择结果
    },
    //查询所有收货人信息
    async getAdrInfo() {
      try {
        uni.showLoading({
          title: "加载中",
        });


        let data = {
          userId:  getUserId(),
          logicalDel: 0,
        };

        const res = await queryAllAddr(data);
        this.addressList = res.data;
        if (res.data && res.data.length > 0) {
          this.selectedAddress = res.data[0].addressId;
        }
      } catch (err) {
        console.error("获取地址信息失败", err);
      } finally {
        uni.hideLoading();
      }
    },
    // 滑动删除相关方法
    onTouchStart(e) {
      this.touchStartX = e.touches[0].clientX;
      this.touchStartY = e.touches[0].clientY;
    },

    onTouchMove(e) {
      // 可以在这里添加滑动过程中的处理逻辑
    },

    onTouchEnd(e) {
      const deltaX = e.changedTouches[0].clientX - this.touchStartX;
      const deltaY = e.changedTouches[0].clientY - this.touchStartY;

      // 判断是否为水平滑动且滑动距离足够
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
        if (deltaX < -50) {
          // 左滑，显示删除按钮
          // 这里可以触发显示删除操作
          console.log("左滑删除");
        }
      }
    },
  },

  onShow() {
    this.getAdrInfo();
  },
};
</script>

<style>
page {
  height: 100%;
}

.address-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  height: 88rpx;
  padding: 0 32rpx;
  background-color: #ffffff;
  flex-shrink: 0;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-right: 44rpx;
}

.address-list {
  flex: 1;
  overflow: auto;
  padding: 24rpx;
  box-sizing: border-box;
}

.address-item {
  /* margin-bottom: 24rpx; */
  border-radius: 12rpx;
  /* background-color: #FFFFFF; */
  overflow: hidden;
}

.address-content {
  padding: 32rpx;
  position: relative;
  background-color: #fff;
  display: flex;
  align-items: center;
}

.radio-container {
  margin-right: 20rpx;
}

.address-info {
  flex: 1;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.name {
  font-size: 16px;
  color: #333333;
  margin-right: 24rpx;
}

.phone {
  font-size: 14px;
  color: #666666;
}

.address {
  font-size: 14px;
  color: #666666;
  line-height: 1.4;
}

.operation {
  position: absolute;
  right: 32rpx;
  top: 32rpx;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 14px;
  color: #999999;
}

.footer {
  padding: 32rpx;
  background-color: #ffffff;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.add-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #ffffff;
  color: #ff4d4f;
  font-size: 16px;
  border-radius: 44rpx;
  border: 1px solid #ff4d4f;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: #ff4d4f;
  color: #ffffff;
  font-size: 16px;
  border-radius: 44rpx;
}

.swipe-content {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}

/* 滑动删除样式 */
.swipe-container {
  position: relative;
}

.swipe-item {
  position: relative;
  display: flex;
  align-items: center;
}

.delete-btn {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 80px;
  background-color: #ff3b30;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.delete-text {
  color: #fff;
  font-size: 14px;
}

.address-content {
  width: 100%;
  transition: transform 0.3s ease;
}
</style>
