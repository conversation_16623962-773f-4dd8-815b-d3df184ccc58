# ===================================
# Node.js & NPM
# ===================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Package manager lock files (uncomment if you want to ignore them)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# ===================================
# Build & Distribution
# ===================================
dist/
build/
out/
.output/
.nuxt/
.next/
.vuepress/dist/
.docusaurus/build/

# ===================================
# Environment & Configuration
# ===================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# ===================================
# Testing & Coverage
# ===================================
coverage/
*.lcov
.nyc_output/

# ===================================
# Logs
# ===================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===================================
# Runtime & Cache
# ===================================
.cache/
.parcel-cache/
.eslintcache
.stylelintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===================================
# Temporary Files
# ===================================
*.tmp
*.temp
.tmp/
.temp/

# ===================================
# Operating System
# ===================================
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===================================
# IDEs & Editors
# ===================================
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/*.code-snippets

# JetBrains IDEs
.idea/
*.swp
*.swo
*~

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

# Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Visual Studio
*.suo
*.user
*.userosscache
*.sln.docstates
*.ntvs*
*.njsproj
*.sln
*.sw?

# ===================================
# Dependencies & Modules
# ===================================
jspm_packages/
web_modules/
node_modules.zip

# ===================================
# Optional npm cache directory
# ===================================
.npm

# ===================================
# Optional eslint cache
# ===================================
.eslintcache

# ===================================
# Microbundle cache
# ===================================
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===================================
# Optional REPL history
# ===================================
.node_repl_history

# ===================================
# Output of 'npm pack'
# ===================================
*.tgz

# ===================================
# Yarn Integrity file
# ===================================
.yarn-integrity

# ===================================
# dotenv environment variables file
# ===================================
.env.test

# ===================================
# parcel-bundler cache (https://parceljs.org/)
# ===================================
.cache
.parcel-cache

# ===================================
# Next.js build output
# ===================================
.next

# ===================================
# Nuxt.js build / generate output
# ===================================
.nuxt
dist

# ===================================
# Gatsby files
# ===================================
.cache/
public

# ===================================
# Vuepress build output
# ===================================
.vuepress/dist

# ===================================
# Serverless directories
# ===================================
.serverless/

# ===================================
# FuseBox cache
# ===================================
.fusebox/

# ===================================
# DynamoDB Local files
# ===================================
.dynamodb/

# ===================================
# TernJS port file
# ===================================
.tern-port

# ===================================
# Stores VSCode versions used for testing VSCode extensions
# ===================================
.vscode-test

# ===================================
# yarn v2
# ===================================
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ===================================
# Project Specific
# ===================================
# Vue.js specific
*.local

# Element UI specific
theme/

# Mock data (if you don't want to commit mock files)
# mock/

# Static assets that shouldn't be tracked
# public/static/

# Auto-generated files
auto-imports.d.ts
components.d.ts

# ===================================
# Database
# ===================================
*.sqlite
*.sqlite3
*.db

# ===================================
# Security & Sensitive Data
# ===================================
# API keys and secrets
.env.secret
secrets.json
config/secrets.js

# SSL certificates
*.pem
*.key
*.crt
*.csr

# ===================================
# Deployment
# ===================================
# Docker
.dockerignore
Dockerfile.prod

# Kubernetes
k8s/secrets/

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# ===================================
# Monitoring & Analytics
# ===================================
# Sentry
.sentryclirc

# ===================================
# Backup files
# ===================================
*.bak
*.backup
*.old

# ===================================
# Archive files
# ===================================
*.zip
*.tar.gz
*.rar
*.7z

# ===================================
# Documentation build
# ===================================
docs/build/
docs/dist/

# ===================================
# Storybook
# ===================================
storybook-static/

# ===================================
# Chromatic
# ===================================
build-storybook.log
