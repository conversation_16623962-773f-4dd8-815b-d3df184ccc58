# UniApp 构建输出目录
unpackage/
dist/
build/

# 依赖包目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
*.lcov

# nyc 测试覆盖率
.nyc_output

# Grunt 中间存储
.grunt

# Bower 依赖目录
bower_components

# node-waf 配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1 声明文件
typings/

# TypeScript 缓存
*.tsbuildinfo

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# Microbundle 缓存
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可选的 REPL 历史
.node_repl_history

# yarn v2 的输出
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# parcel-bundler 缓存
.cache
.parcel-cache

# Next.js 构建输出
.next

# Nuxt.js 构建 / 生成输出
.nuxt

# Gatsby 文件
.cache/
public

# Storybook 构建输出
.out
.storybook-out

# Rollup.js 默认构建输出
dist/

# Serverless 目录
.serverless/

# FuseBox 缓存
.fusebox/

# DynamoDB Local 文件
.dynamodb/

# TernJS 端口文件
.tern-port

# Stores VSCode 版本用于远程容器
.vscode-server

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows 图像文件缓存
Thumbs.db
ehthumbs.db

# Windows 文件夹配置文件
Desktop.ini

# Windows 回收站
$RECYCLE.BIN/

# Windows 快捷方式文件
*.lnk

# Linux
*~

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 日志文件
logs
*.log

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE 和编辑器文件
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Visual Studio
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs

# MonoDevelop
*.userprefs

# Xamarin Studio
*.userprefs

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置文件（根据需要调整）
config/local.js
config/production.local.js
config/development.local.js

# 证书和密钥文件
*.pem
*.key
*.crt
*.csr
*.p12
*.pfx

# 微信小程序相关
project.private.config.json

# HBuilderX 相关
.hbuilderx/

# 其他构建工具
.webpack/
.rollup.cache/
.terser-cache/

# 测试相关
test-results/
coverage/
.nyc_output/

# 文档生成
docs/

# 本地开发配置
.local
local.config.js

# 性能分析文件
*.cpuprofile
*.heapprofile