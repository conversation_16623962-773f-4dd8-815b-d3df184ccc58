/**
 * 微信支付工具类
 * 用于处理PC端WebView中的微信支付功能
 */

class WeixinPayUtil {
  constructor() {
    this.isReady = false;
    this.initPromise = this.init();
  }

  /**
   * 初始化微信支付环境
   */
  async init() {
    return new Promise((resolve, reject) => {
      // 检查是否在微信环境中
      if (!this.isWeixinBrowser()) {
        console.warn('不在微信浏览器环境中');
        resolve(false);
        return;
      }

      // 检查是否在小程序WebView中
      if (this.isInMiniProgram()) {
        console.log('在小程序WebView环境中，使用小程序支付');
        this.isReady = true;
        resolve(true);
        return;
      }

      // 在微信浏览器中，等待WeixinJSBridge准备就绪
      if (typeof WeixinJSBridge !== "undefined") {
        this.isReady = true;
        resolve(true);
      } else {
        document.addEventListener('WeixinJSBridgeReady', () => {
          this.isReady = true;
          resolve(true);
        }, false);

        // 超时处理
        setTimeout(() => {
          if (!this.isReady) {
            console.error('WeixinJSBridge初始化超时');
            reject(new Error('微信支付环境初始化超时'));
          }
        }, 10000);
      }
    });
  }

  /**
   * 检查是否在微信浏览器中
   */
  isWeixinBrowser() {
    return /micromessenger/i.test(navigator.userAgent);
  }

  /**
   * 检查是否在小程序WebView中
   */
  isInMiniProgram() {
    return window.wx && window.wx.miniProgram && window.__wxjs_environment === 'miniprogram';
  }

  /**
   * 发起支付
   * @param {Object} paymentData 支付参数
   * @returns {Promise}
   */
  async pay(paymentData) {
    try {
      await this.initPromise;

      if (!this.isReady) {
        throw new Error('微信支付环境未准备就绪');
      }

      // 验证支付参数
      this.validatePaymentData(paymentData);

      if (this.isInMiniProgram()) {
        // 在小程序WebView中，通过postMessage发送支付数据
        return this.payInMiniProgram(paymentData);
      } else {
        // 在微信浏览器中，直接调用微信支付
        return this.payInBrowser(paymentData);
      }
    } catch (error) {
      console.error('支付失败:', error);
      throw error;
    }
  }

  /**
   * 验证支付参数
   */
  validatePaymentData(paymentData) {
    const requiredFields = ['appid', 'timestamp', 'noncestr', 'package', 'signtype', 'paysign'];
    const missingFields = requiredFields.filter(field => !paymentData[field]);
    
    if (missingFields.length > 0) {
      throw new Error(`支付参数不完整，缺少字段: ${missingFields.join(', ')}`);
    }
  }

  /**
   * 在小程序WebView中发起支付
   */
  payInMiniProgram(paymentData) {
    return new Promise((resolve, reject) => {
      try {
        console.log('在小程序WebView中发起支付');

        // 通过postMessage发送支付数据给小程序（统一使用数组格式）
        window.wx.miniProgram.postMessage({
          data: [{
            type: 'payment',
            paymentData: paymentData
          }]
        });

        console.log('支付数据已发送到小程序:', paymentData);

        // 小程序支付结果需要通过其他方式获取
        // 这里暂时返回成功，实际结果由小程序端处理
        resolve({
          success: true,
          message: '支付请求已发送到小程序'
        });
      } catch (error) {
        console.error('发送支付数据到小程序失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 在微信浏览器中发起支付
   */
  payInBrowser(paymentData) {
    return new Promise((resolve, reject) => {
      if (typeof WeixinJSBridge === "undefined") {
        reject(new Error('WeixinJSBridge未定义'));
        return;
      }

      WeixinJSBridge.invoke(
        'getBrandWCPayRequest',
        {
          appId: paymentData.appid,
          timeStamp: paymentData.timestamp,
          nonceStr: paymentData.noncestr,
          package: paymentData.package,
          signType: paymentData.signtype,
          paySign: paymentData.paysign
        },
        (res) => {
          console.log('微信支付结果:', res);
          
          if (res.err_msg === "get_brand_wcpay_request:ok") {
            resolve({
              success: true,
              message: '支付成功',
              result: res
            });
          } else if (res.err_msg === "get_brand_wcpay_request:cancel") {
            reject({
              success: false,
              message: '用户取消支付',
              cancelled: true,
              result: res
            });
          } else {
            reject({
              success: false,
              message: '支付失败',
              result: res
            });
          }
        }
      );
    });
  }

  /**
   * 获取支付环境信息
   */
  getEnvironmentInfo() {
    return {
      isWeixinBrowser: this.isWeixinBrowser(),
      isInMiniProgram: this.isInMiniProgram(),
      hasWeixinJSBridge: typeof WeixinJSBridge !== "undefined",
      isReady: this.isReady,
      userAgent: navigator.userAgent
    };
  }
}

// 创建单例实例
const weixinPayUtil = new WeixinPayUtil();

export default weixinPayUtil;

/**
 * 便捷的支付函数
 * @param {Object} paymentData 支付参数
 * @returns {Promise}
 */
export function weixinPay(paymentData) {
  return weixinPayUtil.pay(paymentData);
}

/**
 * 获取支付环境信息
 * @returns {Object}
 */
export function getPaymentEnvironment() {
  return weixinPayUtil.getEnvironmentInfo();
}
