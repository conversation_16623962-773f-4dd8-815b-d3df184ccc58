<template>
  <view class="performance-container">
    <!-- 顶部导航栏 -->
    <view class="nav-header">
      <view class="nav-title">业绩管理</view>
      <view class="nav-subtitle">查看代理业绩和团队表现</view>
    </view>

    <!-- 统计信息栏 -->
    <view class="stats-header">
      <view class="stats-item">
        <view class="stats-label">代理个数</view>
        <view class="stats-value">{{ performanceObj.myProxy.length }}</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-label">个人业绩</view>
        <view class="stats-value">{{ performanceObj.self.money }} ￥</view>
      </view>
      <view class="stats-divider"></view>
      <view class="stats-item">
        <view class="stats-label">总体业绩</view>
        <view class="stats-value">{{ sum }} ￥</view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-wrapper">
      <!-- 空状态 -->
      <view v-if="showList.length === 0" class="empty-state">
        <uni-icons type="shop" size="60" color="#ccc"></uni-icons>
        <text class="empty-text">{{ msg }}</text>
        <text class="empty-desc">您还没有任何代理业绩数据</text>
      </view>

      <!-- 代理列表 -->
      <view class="proxy-list" v-else>
        <view class="proxy-card" v-for="(data, index) in showList" :key="index" @click="toProxyDetail(data)">
          <!-- 代理头部 -->
          <view class="proxy-header">
            <view class="proxy-title">
              <view class="avatar">
                <image :src="data.headPic" class="avatar-image"></image>
              </view>
              <view class="proxy-info">
                <view class="proxy-name">{{ data.userName }}</view>
                <view class="proxy-date">{{ formatTiem(data.createTime) }}</view>
              </view>
            </view>
            <view class="arrow">
              <uni-icons type="right" size="16" color="#ccc"></uni-icons>
            </view>
          </view>

          <!-- 代理详情 */
          <view class="proxy-details">
            <view class="detail-row">
              <text class="detail-label">手机尾号</text>
              <text class="detail-value">{{ getPhoneTail(data.phone) }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">营业额</text>
              <text class="detail-price">{{ data.money === null ? "0" : data.money }} ￥</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  showPerformance,
  showProxyPerformance,
} from "@/subpackages/proxy/api/proxyPerformance.js";
import { getDateByDay } from "@/utils/dateUtil.js";
import { isLogin, isProxyLogin } from "@/utils/auth.js";

const toLocaleDateString = function (data = new Date()) {
  let res = "";
  res += data.getFullYear() + "/";
  res += data.getMonth() + 1 + "/";
  res += data.getDate();
  return res;
};

export default {
  data() {
    return {
      checkNav: 0,
      performanceObj: {
        ohtherProxy: [],
        self: {
          money: 0,
        },
        myProxy: [],
      },
      showList: [],
      msg: "加载中",
      sum: 0,
      dateRange: [],
    };
  },
  methods: {
    clickNav(index) {
      this.checkNav = index;
      if (index !== 3) {
        let start;
        let end = toLocaleDateString(getDateByDay(0));
        if (index === 0) {
          start = end;
        } else if (index === 1) {
          start = toLocaleDateString(getDateByDay(6));
        } else if (index === 2) {
          start = toLocaleDateString(getDateByDay(30));
        }
        this.dateRange = [start, end];
      }
    },
    showPerformance() {
      showProxyPerformance().then((res) => {
        if (res.data.code !== 200) {
          return;
        }
        this.performanceObj = res.data.data;
        this.index = 0;
        this.showList = this.performanceObj.myProxy;
        let sum = 0;
        this.performanceObj.myProxy.forEach((i) => {
          if (i.money != null) {
            sum += i.money;
          }
        });
        sum += this.performanceObj.self.money;
        this.sum = sum.toFixed(2);
        this.msg = "暂无数据";
      });
    },
    formatTiem(timer = "") {
      return timer.split(" ")[0].replaceAll("/", "-");
    },
    getPhoneTail(phone = "") {
      return phone.substring(phone.length - 4);
    },
    toProxyDetail(proxy) {
      const data = {
        proxy,
        range: this.dateRange.map((o = "") => o.replaceAll("-", "/")),
        checkNav: this.checkNav,
      };
      uni.navigateTo({
        url:
          "/subpackages/proxy/pages/proxyProxyPerformance/proxyPerformance?data=" +
          JSON.stringify(data),
      });
    },
  },
  onLoad() {
    // 检查代理端登录状态
    if (!isLogin() || !isProxyLogin()) {
      console.log("用户未登录或非代理端登录，跳转到统一登录页");
      uni.reLaunch({
        url: "/pages/unifiedLogin/unifiedLogin?mode=proxy",
      });
      return;
    }
    this.showPerformance();
    const start = toLocaleDateString(getDateByDay(0));
    this.dateRange = [start, start];
  },
};
</script>

<style lang="scss">
/* 页面容器 - 统一用户端样式 */
.performance-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
}

/* 顶部导航栏 - 统一用户端样式 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 统计信息栏 - 优化样式 */
.stats-header {
  background: white;
  padding: 20px;
  margin: 0 20px;
  margin-top: -15px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 212, 170, 0.15);
  display: flex;
  justify-content: space-around;
  align-items: center;
  position: relative;
  z-index: 1;

  .stats-item {
    text-align: center;
    flex: 1;

    .stats-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .stats-value {
      font-size: 18px;
      font-weight: 600;
      color: #00d4aa;
      letter-spacing: 0.3px;
    }
  }

  .stats-divider {
    width: 1px;
    height: 40px;
    background-color: #f0f0f0;
    margin: 0 15px;
  }
}

/* 内容区域 */
.content-wrapper {
  padding: 20px;
}

/* 空状态 - 统一用户端样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;

  .empty-text {
    font-size: 16px;
    color: #999;
    margin: 20px 0 8px;
    font-weight: 500;
  }

  .empty-desc {
    font-size: 14px;
    color: #ccc;
    line-height: 1.5;
  }
}

/* 代理列表 */
.proxy-list {
  .proxy-card {
    background: white;
    border-radius: 16px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }

    /* 代理头部 */
    .proxy-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 20px 16px;
      border-bottom: 1px solid #f5f5f5;

      .proxy-title {
        display: flex;
        align-items: center;
        flex: 1;

        .avatar {
          margin-right: 15px;

          .avatar-image {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            border: 2px solid #00d4aa;
            transition: all 0.3s ease;
          }
        }

        .proxy-info {
          flex: 1;

          .proxy-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 6px;
            letter-spacing: 0.3px;
          }

          .proxy-date {
            font-size: 13px;
            color: #666;
            font-weight: 500;
          }
        }
      }

      .arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        border-radius: 12px;
        background: rgba(0, 212, 170, 0.1);
        transition: all 0.3s ease;

        &:active {
          background: rgba(0, 212, 170, 0.2);
          transform: scale(0.95);
        }
      }
    }

    /* 代理详情 */
    .proxy-details {
      padding: 16px 20px;

      .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .detail-label {
          font-size: 14px;
          color: #666;
          font-weight: 500;
          min-width: 80px;
        }

        .detail-value {
          font-size: 14px;
          color: #333;
          font-weight: 500;
          text-align: right;
        }

        .detail-price {
          font-size: 16px;
          color: #00d4aa;
          font-weight: 600;
          text-align: right;
        }
      }
    }
  }
}
</style>
