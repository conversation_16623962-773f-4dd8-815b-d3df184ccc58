// Element UI 真正的按需引入配置
import Vue from 'vue'

// 基础组件
import {
  Button,
  ButtonGroup,
  Select,
  Option,
  OptionGroup,
  Input,
  InputNumber,
  Radio,
  RadioGroup,
  RadioButton,
  Checkbox,
  CheckboxButton,
  CheckboxGroup,
  Switch,
  Slider,
  TimeSelect,
  TimePicker,
  DatePicker,
  Upload,
  Rate,
  ColorPicker,
  Transfer,
  Form,
  FormItem,
  Table,
  TableColumn,
  Tag,
  Progress,
  Tree,
  Pagination,
  Badge,
  Avatar
} from 'element-ui'

// 布局组件
import { Row, Col, Container, Header, Aside, Main, Footer } from 'element-ui'

// 导航组件
import {
  Menu,
  Submenu,
  MenuItem,
  MenuItemGroup,
  Tabs,
  TabPane,
  Breadcrumb,
  BreadcrumbItem,
  PageHeader,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Steps,
  Step
} from 'element-ui'

// 反馈组件
import {
  Alert,
  Loading,
  Message,
  MessageBox,
  Notification,
  Dialog,
  Popover,
  Popconfirm,
  Tooltip
} from 'element-ui'

// 其他组件
import {
  Card,
  Carousel,
  CarouselItem,
  Collapse,
  CollapseItem,
  Timeline,
  TimelineItem,
  Divider,
  Calendar,
  Image,
  Backtop,
  Empty,
  Descriptions,
  DescriptionsItem,
  Result,
  Statistic,
  Skeleton,
  SkeletonItem,
  Space,
  Affix,
  Icon,
  Link,
  Scrollbar
} from 'element-ui'

// InfiniteScroll 是一个指令，需要单独引入
import { InfiniteScroll } from 'element-ui'

// 高级组件
import { Cascader, CascaderPanel, Autocomplete, Drawer } from 'element-ui'

import locale from 'element-ui/lib/locale/lang/zh-CN'

// 所有组件列表
const components = [
  Button,
  ButtonGroup,
  Select,
  Option,
  OptionGroup,
  Input,
  InputNumber,
  Radio,
  RadioGroup,
  RadioButton,
  Checkbox,
  CheckboxButton,
  CheckboxGroup,
  Switch,
  Slider,
  TimeSelect,
  TimePicker,
  DatePicker,
  Upload,
  Rate,
  ColorPicker,
  Transfer,
  Form,
  FormItem,
  Table,
  TableColumn,
  Tag,
  Progress,
  Tree,
  Pagination,
  Badge,
  Avatar,
  Row,
  Col,
  Container,
  Header,
  Aside,
  Main,
  Footer,
  Menu,
  Submenu,
  MenuItem,
  MenuItemGroup,
  Tabs,
  TabPane,
  Breadcrumb,
  BreadcrumbItem,
  PageHeader,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Steps,
  Step,
  Alert,
  Card,
  Carousel,
  CarouselItem,
  Collapse,
  CollapseItem,
  Timeline,
  TimelineItem,
  Divider,
  Calendar,
  Image,
  Backtop,
  Empty,
  Descriptions,
  DescriptionsItem,
  Result,
  Statistic,
  Skeleton,
  SkeletonItem,
  Space,
  Affix,
  Icon,
  Link,
  Scrollbar,
  Cascader,
  CascaderPanel,
  Autocomplete,
  Drawer,
  Dialog,
  Popover,
  Popconfirm,
  Tooltip
]

export default {
  install() {
    // 注册所有组件
    components.forEach((component) => {
      if (component && component.name) {
        Vue.component(component.name, component)
      }
    })

    // 注册指令和服务
    Vue.use(Loading.directive)
    Vue.use(InfiniteScroll)

    // 挂载全局方法
    Vue.prototype.$loading = Loading.service
    Vue.prototype.$msgbox = MessageBox
    Vue.prototype.$alert = MessageBox.alert
    Vue.prototype.$confirm = MessageBox.confirm
    Vue.prototype.$prompt = MessageBox.prompt
    Vue.prototype.$notify = Notification
    Vue.prototype.$message = Message

    // 设置语言
    Vue.prototype.$ELEMENT = { locale }
  }
}
