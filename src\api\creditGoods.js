import { get, post } from "@/utils/requestMethod";

/**
 * 获取话费配置列表
 */
const getCreditGoodsList = function (data) {
  return get("/creditgoods/list", data);
};

/**
 * 根据ID获取话费配置详情
 */
const getCreditGoodsDetail = function (id) {
  return get(`/creditgoods/get/${id}`);
};

/**
 * 更新/编辑话费配置
 */
const updateCreditGoods = function (data) {
  return post("/creditgoods/update", data);
};

/**
 * 删除话费配置
 */
const deleteCreditGoods = function (id) {
  return post("/creditgoods/delete", { id });
};

/**
 * 获取话费商品列表 (用户端)
 */
const getCreditGoods = function (data) {
  return post("/creditgoods/getCreditGoods", data);
};

export {
  getCreditGoodsList,
  getCreditGoodsDetail,
  updateCreditGoods,
  deleteCreditGoods,
  getCreditGoods,
};
