import {
  removeToken,
  removeUserId,
  clearProxyLoginStatus,
  resetInfo,
} from "./auth.js";
import { redirectAfterLogout } from "./routeGuard.js";
import store from "@/store";

/**
 * 统一登出函数
 * @param {Object} options - 登出选项
 * @param {boolean} options.showMessage - 是否显示登出提示消息
 * @param {string} options.message - 自定义提示消息
 */
export function logout(options = {}) {
  const { showMessage = true, message = "已退出登录" } = options;

  try {
    // 清除所有登录相关的本地存储
    resetInfo();

    // 清除token和用户ID
    removeToken();
    removeUserId();

    // 清除代理端登录状态
    clearProxyLoginStatus();

    // 清除Vuex中的用户信息
    if (store.state.user) {
      store.commit("user/DEFAULTUSERINFO");
    }
    if (store.state.proxyUser) {
      store.commit("proxyUser/DEFAULTUSERINFO");
    }

    console.log("[Logout] 登出成功，已清除所有登录状态");

    // 显示提示消息
    if (showMessage) {
      uni.showToast({
        title: message,
        icon: "success",
        duration: 1500,
      });
    }

    // 延迟跳转，让用户看到提示消息
    setTimeout(
      () => {
        redirectAfterLogout();
      },
      showMessage ? 1500 : 0
    );

    return true;
  } catch (error) {
    console.error("[Logout] 登出过程中出现错误:", error);

    // 即使出错也要跳转到登录页
    redirectAfterLogout();

    return false;
  }
}

/**
 * 强制登出（不显示提示消息，立即跳转）
 */
export function forceLogout() {
  return logout({ showMessage: false });
}

/**
 * 会话过期登出
 */
export function sessionExpiredLogout() {
  return logout({
    showMessage: true,
    message: "会话已过期，请重新登录",
  });
}
