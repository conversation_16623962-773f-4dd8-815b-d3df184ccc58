<template>
  <view class="bkcolor">
    <!-- 使用原生 loading 替代 u-loading-page -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 图片 -->
    <view style="background-color: white; width: 100vw; height: 100vw">
      <view
        style="color: #7f7f7f; text-align: center; line-height: 100vw"
        v-if="isLoad"
      >
        加载中
      </view>
      <image
        :src="image2"
        style="width: calc(100vw - 20px); border-radius: 10px"
        class="wd"
        mode="widthFix"
        v-else
      >
      </image>
    </view>
    <!-- 价格 -->
    <view
      style="
        background: linear-gradient(to right, #fe8c07 5%, #fe1a18);
        border-radius: 10px 10px 0 0;
        display: flex;
      "
    >
      <view>
        <view
          class="wd"
          style="
            color: white;
            font-weight: bolder;
            padding: 10px 0 5px 0;
            font-size: 13px;
          "
        >
          原价
        </view>
        <!-- 价格 -->
        <view
          class="wd"
          style="padding-bottom: 10px; color: white; font-weight: bolder"
        >
          <text style="font-size: 13px">￥</text>
          <text style="font-size: 18px">{{ good.face_value }}</text>
        </view>
      </view>
      <view>
        <view
          class="wd"
          style="
            color: white;
            font-weight: bolder;
            padding: 10px 0 5px 0;
            font-size: 13px;
          "
        >
          权益价
        </view>
        <!-- 价格 -->
        <view
          class="wd"
          style="padding-bottom: 10px; color: white; font-weight: bolder"
        >
          <text style="font-size: 13px">￥</text>
          <text style="font-size: 18px">{{ good.purchase_price }}</text>
        </view>
      </view>
      <!-- <view>
				<view class="wd" style="color: white; font-weight: bolder; padding: 10px 0 5px 0; font-size: 13px;">
					消耗积分
				</view>
				
				<view class="wd" style="padding-bottom: 10px; color: white; font-weight: bolder;">
					<text style="font-size: 13px;"></text> <text
						style="font-size: 18px;">{{good.face_value - good.purchase_price}}</text>
				</view>
			</view> -->
    </view>
    <!-- 商品信息 -->
    <view class="wd fl-good-info">
      <view>{{ good.product_name }}</view>
      <view>商品类型:{{ good.product_type }}</view>
      <view>库存:{{ good.stock_status }}</view>
    </view>
    <!--  底部 -->
    <view
      style="
        position: fixed;
        bottom: 0;
        width: 100vw;
        height: 60px;
        background-color: white;
      "
    >
      <view
        style="
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: space-around;
        "
      >
        <view
          style="
            padding: 5px 15vw;
            background-color: #3f85fc;
            border-radius: 15px;
            color: white;
          "
          @click="isShow = true"
        >
          点击购买
        </view>
      </view>
    </view>
    <!-- 弹出弹框 -->
    <bottom-message
      @closeMsg="isShow = false"
      :isShow="isShow"
      title="订单信息"
    >
      <view class="">
        <!-- 填写充值账号 -->
        <view style="display: flex; align-items: center; font-size: 14px">
          <view style="font-weight: bolder">充值账号:</view>
          <view style="margin-left: 8px"
            ><input placeholder="请输入账号" v-model="username"
          /></view>
        </view>
        <!-- 商品介绍 -->
        <view
          style="
            margin-top: 10px;
            display: flex;
            align-items: center;
            margin-bottom: 50vh;
          "
          class="wd"
        >
          <view>
            <image
              :src="image2"
              style="width: 100px; height: 100px; border-radius: 10px"
            ></image>
          </view>
          <view style="margin-left: 20px">
            <view style="color: #e02e24; font-weight: bold">
              <text>权益价</text>
              <text style="margin-left: 5px">￥{{ good.purchase_price }}</text>
            </view>
            <view style="margin-top: 5px">
              {{ good.product_name }}
            </view>
            <!-- 购买数量 -->
            <view
              style="
                display: flex;
                justify-content: left;
                align-items: center;
                margin-top: 15px;
              "
            >
              <view
                class="gi-buttom"
                @click="numButtom(-1)"
                :style="num <= 1 ? 'color: #F4F4F4;' : ''"
              >
                -
              </view>
              <view
                style="
                  height: 30px;
                  line-height: 30px;
                  margin: 0 10px;
                  width: 30px;
                "
              >
                <input
                  type="text"
                  inputmode="numeric"
                  v-model="num"
                  style="height: 30px"
                />
              </view>
              <view class="gi-buttom" @click="numButtom(1)"> + </view>
            </view>
          </view>
        </view>
        <view style="width: 100vw; height: 60px; background-color: white">
          <view
            style="
              height: 60px;
              display: flex;
              align-items: center;
              justify-content: space-around;
            "
          >
            <view style="display: flex; padding: 5px 0">
              <image
                :src="`${config.BASE_URL}/static/wx.png`"
                style="width: 30px; height: 30px"
              ></image>
              <view> 微信支付 </view>
            </view>
            <view
              style="
                padding: 5px 15vw;
                background-color: #3f85fc;
                border-radius: 15px;
                color: white;
              "
              @click="payGoods"
            >
              立即支付
            </view>
          </view>
        </view>
      </view>
    </bottom-message>
  </view>
</template>

<script>
import {
  userGetGoodsInfo,
  creatOrder,
  flAppPayForPrepayid,
} from "@/api/fuLu.js";
import { mapGetters } from "vuex";
import bottomMessage from "../../components/bottomMessage.vue";
import { fuioupay } from "@/api/fuiou.js";
import { getUserId } from "@/utils/auth.js";
import config from "@/utils/config.js";
export default {
  components: {
    bottomMessage,
  },
  data() {
    return {
      config,
      good: {},
      isLoad: false,
      isShow: false,
      num: 1,
      username: "",
      image2: "",
      loading: true,
    };
  },
  methods: {
    userGetGoodsInfo(product_id) {
      const data = {
        product_id,
      };
      this.isLoad = true;
      userGetGoodsInfo(data)
        .then((res) => {
          res = res.data;
          if (res.code !== 200) {
            return;
          }
          console.log(res.data);
          this.good = res.data;
          this.loading = false;
        })
        .finally(() => {
          this.isLoad = false;
          this.loading = false;
        });
    },
    numButtom(e) {
      const res = parseInt(this.num) + e;
      if (res <= 0) {
        return;
      }
      this.num = res;
    }, // 点击支付
    payGoods() {
      if (this.username.trim().length <= 0) {
        uni.showToast({
          title: "请填写账号",
          icon: "error",
        });
        return;
      }

      // 获取用户ID
      const userId = getUserId();
      if (!userId) {
        uni.showToast({
          title: "请先登录",
          icon: "error",
        });
        return;
      }

      let data = {
        userId: userId,
        type: "1",
        name: this.good.product_name,
        // phone: this.phone,
        product_id: this.good.product_id,
        charge_account: this.username,
        buy_num: this.num,
        // 待添加金额字段
        amt: this.good.purchase_price,
      };
      console.log(data);
      // 调起富友支付
      console.log("发起支付");
      fuioupay(data).then((res) => {
        // uni.hideLoading()
        // 如果返回code == 200
        if (res.data.code == "200") {
          console.log("请求成功");
          let redata = res.data;

          console.log("开始调起微信支付");

          // 如果已经在微信环境中，直接调用
          console.log(redata);
          uni.requestPayment({
            appId: res.data.data.appid, // 公众号ID，由商户传入
            timeStamp: res.data.data.timestamp, // 时间戳，自1970年以来的秒数
            nonceStr: res.data.data.noncestr, // 随机串
            package: res.data.data.package,
            signType: res.data.data.signtype, // 微信签名方式
            paySign: res.data.data.paysign, // 微信签名
            success: function (res) {
              console.log(res); //res.errMsg == "requestPayment:ok"
              console.log("success:" + JSON.stringify(res));
              uni.switchTab({
                url: "../my/my",
              });
            },
            fail: function (err) {
              console.log("fail:" + JSON.stringify(err));
            },
            complete(err) {
              console.log("complete:" + JSON.stringify(err));
            },
          });
        } else {
          uni.showToast({
            title: res.data.message,
            icon: "error",
          });
        }
      });
    },
    // 创建订单
    createOrder() {
      if (this.username.trim().length <= 0) {
        return Promise.reject({
          msg: "未输入用户名",
        });
      }
      const data = {
        product_name: this.good.product_name,
        // phone: this.phone,
        product_id: this.good.product_id,
        charge_account: this.username,
        buy_num: this.num,
        phone: this.userInfo.phone,
      };
      console.log(data);
      return creatOrder(data);
    },
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  onLoad(param) {
    this.userGetGoodsInfo(parseInt(param.product_id));
    this.image2 = param.image2;
  },
};
</script>

<style>
.fl-good-info {
  margin-top: 10px;
  background-color: white;
  padding: 10px 10px;
  border-radius: 10px;
  font-weight: bolder;
  font-size: 15px;
}

.gi-buttom {
  width: 30px;
  height: 30px;
  text-align: center;
  font-size: 20px;
  background-color: #f4f4f4;
}

/* Loading overlay styles */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3f85fc;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
