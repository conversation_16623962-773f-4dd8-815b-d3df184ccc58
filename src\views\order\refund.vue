﻿<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-card">
        <div class="search-header">
          <i class="el-icon-search search-icon" />
          <span class="search-title">话费充值订单查询</span>
        </div>

        <div class="search-content">
          <div class="search-input-wrapper">
            <el-input
              v-model="phone"
              placeholder="充值手机号"
              prefix-icon="el-icon-mobile-phone"
              clearable
            />
          </div>

          <div class="search-input-wrapper">
            <el-input
              v-model="orderno"
              placeholder="内部订单号"
              prefix-icon="el-icon-document"
              clearable
            />
          </div>

          <div class="search-select-wrapper">
            <el-select v-model="refundStatus" placeholder="请选择状态">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>

          <div class="search-button-wrapper">
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="searchOrder"
            >
              查询
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 导出表格区域 -->
    <div class="export-section">
      <div class="export-card">
        <div class="date-picker-wrapper">
          <el-date-picker
            v-model="value1"
            :default-value="new Date()"
            :unlink-panels="true"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            align="right"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            class="date-picker"
            @change="change"
          />
        </div>

        <div class="export-actions">
          <el-button
            type="success"
            icon="el-icon-download"
            @click="downLoadExcex()"
          >
            下载Excel表
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <div class="table-header">
          <div class="table-title">
            <i class="el-icon-mobile-phone" />
            <span>话费充值订单</span>
          </div>
        </div>

        <el-table
          :data="showData"
          stripe
          style="width: 100%"
          class="modern-table"
          :header-cell-style="{
            background: '#f8fafc',
            color: '#374151',
            fontWeight: '600',
          }"
          :row-style="{ height: '60px' }"
        >
          <el-table-column
            prop="systemOrderNo"
            label="外部订单号"
            min-width="140"
          >
            <template slot-scope="scope">
              <span class="order-no">{{ scope.row.systemOrderNo }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="outTradeNum"
            label="内部订单号"
            min-width="140"
          >
            <template slot-scope="scope">
              <span class="order-no internal">{{ scope.row.outTradeNum }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="mobile" label="充值手机号" min-width="130">
            <template slot-scope="scope">
              <span class="phone-number">{{ scope.row.mobile }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="payOrderNo" label="姓名" min-width="120">
            <template slot-scope="scope">
              <span class="customer-name">{{
                scope.row.payOrderNo || "-"
              }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="productTitle" label="话费套餐" min-width="180">
            <template slot-scope="scope">
              <div class="product-name-cell">
                <i class="el-icon-mobile-phone product-icon" />
                <span>{{ scope.row.productTitle }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column
            prop="facePrice"
            label="套餐面值"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span class="price-text">¥{{ scope.row.facePrice }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="realPay"
            label="实付金额"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span class="price-text real-pay">¥{{ scope.row.realPay }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="payTime" label="下单时间" min-width="160">
            <template slot-scope="scope">
              <span class="order-time">{{ scope.row.payTime }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="orderStatus"
            label="下单状态"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag
                :type="getOrderStatusType(scope.row.orderStatus)"
                size="small"
                class="status-tag"
              >
                {{ getOrderStatus(scope.row.orderStatus) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="退款金额" width="120" align="center">
            <template slot-scope="scope">
              <span class="refund-amount"
                >¥{{ getRefund(scope.row.refund) }}</span
              >
            </template>
          </el-table-column>

          <!-- <el-table-column
            prop="orderStatus"
            label="订单状态"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <el-tag
                :type="
                  getRefundStatus(scope.row.refundStatus) === '无退款'
                    ? 'success'
                    : 'danger'
                "
                size="small"
                class="status-tag"
              >
                {{
                  getRefundStatus(scope.row.refundStatus) === "无退款"
                    ? "正常订单"
                    : getRefundStatus(scope.row.refundStatus)
                }}
              </el-tag>
            </template>
          </el-table-column> -->

          <el-table-column
            prop="payPlatform"
            label="支付平台"
            width="120"
            align="center"
          >
            <template slot-scope="scope">
              <span class="pay-platform">{{
                scope.row.payPlatform || "-"
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            label="操作"
            width="120"
            align="center"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="danger"
                plain
                size="small"
                icon="el-icon-refresh-left"
                class="action-btn refund-btn"
                :disabled="scope.row.orderStatus !== 'PAY'"
                @click="clickRefund(scope.row)"
              >
                退款
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <el-pagination
        background
        layout="total, prev, pager, next, jumper"
        :total="filterParam.total"
        :page-size="filterParam.limit"
        :current-page="filterParam.page"
        class="modern-pagination"
        @current-change="changePage"
      />
    </div>
  </div>
</template>

<script>
import * as xlsx from "xlsx";
import { saveAs } from "file-saver";
import { pageByPramas, allcommodity, searchOrderByPage } from "@/api/order";
import { refund, rejuctRefund, refundCredit } from "@/api/refund";
import { getCreditOrderList, getCreditExcel } from "@/api/credit_order";
import { allOrders } from "@/api/excel";

export default {
  data() {
    return {
      showData: [],
      filterParam: {
        page: 1,
        limit: 10,
        total: 0,
        phone: "",
        orderno: "",
      },
      phone: "",
      orderno: "",
      options: [
        {
          label: "支付成功",
          value: "PAY",
        },
        {
          label: "支付失败",
          value: "PAY_FAIL",
        },
        {
          label: "充值中",
          value: "WAIT_TOPUP",
        },
        {
          label: "充值失败",
          value: "FAIL_ORDER",
        },
        {
          label: "充值成功",
          value: "SUCCESS",
        },
        {
          label: "已退款",
          value: "refund",
        },
        {
          label: "全部",
          value: "all",
        },
      ],
      refundStatus: "all",
      dictKey: {},
      value1: "",
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },
  mounted() {
    this.pageByPramas();
    this.allcommodity();
  },
  methods: {
    change(o) {
      console.log(o);
      console.log(this.value1);
    },
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始，需加1，并确保是两位数
      const day = String(date.getDate()).padStart(2, "0"); // 确保日期是两位数
      const hours = String(date.getHours()).padStart(2, "0"); // 确保小时是两位数
      const minutes = String(date.getMinutes()).padStart(2, "0"); // 确保分钟是两位数
      const seconds = String(date.getSeconds()).padStart(2, "0"); // 确保秒数是两位数

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    searchOrder() {
      this.filterParam.phone = this.phone;
      this.filterParam.orderno = this.orderno;
      if (this.refundStatus == "all") {
        this.filterParam.refundStatus = "";
      } else {
        this.filterParam.refundStatus = this.refundStatus;
      }
      console.log(this.filterParam.refundStatus);
      this.pageByPramas();
    },
    getRefund(obj) {
      if (obj == "" || obj == null || obj == undefined) {
        return 0;
      } else {
        return obj;
      }
    },
    pageByPramas() {
      const data = {
        page: this.filterParam.page,
        limit: this.filterParam.limit,
      };
      if (this.filterParam.phone !== "") {
        data.mobile = this.filterParam.phone;
      }
      if (this.filterParam.orderno !== "") {
        data.outTradeNum = this.filterParam.orderno;
      }
      if (this.filterParam.refundStatus !== "") {
        data.orderStatus = this.filterParam.refundStatus;
      }

      getCreditOrderList(data).then(({ data: res }) => {
        this.filterParam.total = res.count;
        const data = res.data;
        this.showData = data;
      });
    },
    changePage(page) {
      this.filterParam.page = page;
      this.pageByPramas();
    },
    // 订单状态映射
    getOrderStatus(obj) {
      if (obj === "PAY") {
        return "支付成功";
      }
      if (obj === "PAY_FAIL") {
        return "支付失败";
      }
      if (obj === "WAIT_PAY") {
        return "待支付";
      }
      if (obj === "WAIT_TOPUP") {
        return "充值中";
      }
      if (obj === "FAIL_ORDER") {
        return "充值失败";
      }
      if (obj === "SUCCESS") {
        return "充值成功";
      }
      if (obj === "refund") {
        return "已退款";
      }
    },
    // 订单状态类型映射 - 用于el-tag的type属性
    getOrderStatusType(obj) {
      if (obj === "PAY") {
        return "success";
      }
      if (obj === "PAY_FAIL") {
        return "danger";
      }
      if (obj === "WAIT_PAY") {
        return "warning";
      }
      if (obj === "WAIT_TOPUP") {
        return "primary";
      }
      if (obj === "FAIL_ORDER") {
        return "danger";
      }
      if (obj === "SUCCESS") {
        return "success";
      }
      if (obj === "refund") {
        return "info";
      }
      return "info"; // 默认类型
    },
    // 商品相关方法
    allcommodity() {
      // 如果需要获取商品字典数据，可以在这里添加API调用
      // 目前为空方法以避免控制台错误
      console.log("allcommodity method called");
    },
    // 退货状态映射
    getRefundStatus(obj) {
      if (obj === "WAIT_REFUND") {
        return "待退款";
      }
      if (obj === "refund") {
        return "已退款";
      }
      if (obj === "FAILED") {
        return "退款失败";
      }
      return "无退款";
    },
    clickRefund(order) {
      console.log(order);
      if (order.refundStatus === "refund") {
        this.$message.error("该订单已退款");
        return;
      }
      this.$prompt("是否对该订单申请退款?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        inputPlaceholder: "请输入退款金额",
        inputPattern: /^(0|([1-9][0-9]*))(\.[\d]+)?$/,
      })
        .then(({ value }) => {
          console.log("打印信息");
          console.log(order);
          console.log(value);
          this.torefund(order, value);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "取消输入",
          });
        });

      // this.$confirm('是否对该订单申请退款?', '提示', {
      // 	confirmButtonText: '确定',
      // 	cancelButtonText: '取消',
      // 	type: 'warning'
      // }).then(() => {
      // 	this.torefund(order)
      // }).catch(() => {
      // 	this.$message({
      // 		type: 'info',
      // 		message: '取消申请退款'
      // 	});
      // })
    },
    // 退款
    torefund(order, value) {
      if (value > order.realPay) {
        this.$message.error("无法大于订单总金额");
        return;
      }

      // 格式化 order_time 字段，将 "/" 替换为 "-"
      const formattedOrderTime = order.payTime
        ? order.payTime.replace(/\//g, "-")
        : "";

      const data = {
        mchnt_order_no: order.outTradeNum,
        refund_amt: value,
        total_amt: order.realPay + "",
        order_time: formattedOrderTime,
      };

      // 申请退款逻辑
      refundCredit(data)
        .then((res) => {
          console.log(res);
          if (res.data.message == "退款成功") {
            this.$message.success("向微信申请退款成功");
            this.pageByPramas();
          } else {
            this.$message.error("退款失败");
          }
        })
        .catch((error) => {
          this.$message.error("退款失败");
          return;
        });
    },
    // 下载订单excel表
    downLoadExcex() {
      // let data = {
      // 	type: "1"
      // }
      // allOrders(data).then(res => {
      // 	console.log(res)
      // 	window.open(res.data.data)
      // }).catch(error => {
      // 	this.$message.error('下载失败')
      // 	return
      // })

      const h = this.$createElement;
      this.$msgbox({
        title: "提示",
        message: h("p", null, [
          h("span", { style: "color: red" }, "是否确认导出excel数据"),
        ]),
        showCancelButton: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        beforeClose: (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = true;
            instance.confirmButtonText = "导出中...";

            const data = {};
            if (this.value1) {
              data.start_time = this.formatDate(this.value1[0]);
              data.end_time = this.formatDate(this.value1[1]);
            }
            console.log(data);
            // 处理excel数据
            getCreditExcel(data).then((res) => {
              if (res.data.message === "success") {
                const data = res.data.data;
                // 使用xlsx库的工作簿和工作表API
                const ws = xlsx.utils.aoa_to_sheet(data); // 将数组转换为工作表
                const wb = xlsx.utils.book_new(); // 创建一个新的工作簿
                xlsx.utils.book_append_sheet(wb, ws, "Sheet1"); // 将工作表添加到工作簿

                // 生成Excel文件
                const wbout = xlsx.write(wb, {
                  bookType: "xlsx",
                  type: "binary",
                });

                // 使用file-saver保存文件
                function s2ab(s) {
                  const buf = new ArrayBuffer(s.length);
                  const view = new Uint8Array(buf);
                  for (let i = 0; i < s.length; i++) {
                    view[i] = s.charCodeAt(i) & 0xff;
                  }
                  return buf;
                }

                const dataName = "话费订单导出数据.xlsx";
                saveAs(
                  new Blob([s2ab(wbout)], { type: "application/octet-stream" }),
                  dataName
                );
                done();
                instance.confirmButtonLoading = false;
              } else {
                this.$message.error("导出数据失败");
              }
            });
          } else {
            done();
          }
        },
      })
        .then((action) => {
          this.$message.success("导出数据成功");
        })
        .catch((action) => {
          this.$message.warning("取消导出数据");
        });
    },
  },
};
</script>

<style scoped>
.app-container {
  padding: 6px;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 8px;
}

.search-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 10px;
  border: 1px solid #e5e7eb;
}

.search-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.search-header .search-icon {
  color: #409eff;
  font-size: 1.1rem;
  margin-right: 6px;
}

.search-header .search-title {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.search-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.search-input-wrapper {
  flex: 1;
  min-width: 200px;
  max-width: 240px;
}

.search-select-wrapper {
  min-width: 160px;
}

.search-button-wrapper {
  min-width: 100px;
}

/* 导出区域样式 */
.export-section {
  margin-bottom: 8px;
}

.export-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 6px;
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.date-picker-wrapper {
  flex: 1;
  min-width: 300px;
}

.date-picker {
  width: 100%;
}

.export-actions {
  display: flex;
  gap: 12px;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 8px;
}

.table-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.table-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e5e7eb 100%);
  padding: 8px 10px;
  border-bottom: 1px solid #e5e7eb;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.table-title i {
  margin-right: 6px;
  color: #409eff;
  font-size: 1.1rem;
}

.order-no {
  font-family: "Monaco", "Menlo", monospace;
  color: #4338ca;
  font-weight: 500;
  font-size: 12px;
}

.order-no.internal {
  color: #7c3aed;
}

.phone-number {
  font-family: "Monaco", "Menlo", monospace;
  color: #059669;
  font-weight: 500;
}

.customer-name {
  color: #6b7280;
}

.product-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-icon {
  color: #f59e0b;
  font-size: 1rem;
}

.price-text {
  font-weight: 600;
  color: #059669;
}

.price-text.real-pay {
  color: #0d9488;
}

.refund-amount {
  font-weight: 600;
  color: #dc2626;
}

.order-time {
  color: #6b7280;
  font-size: 12px;
}

.pay-platform {
  color: #4338ca;
  font-weight: 500;
}

.status-tag {
  font-weight: 600;
}

.action-btn {
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
}

.refund-btn:hover {
  box-shadow: 0 3px 12px rgba(239, 68, 68, 0.4);
}

/* 分页样式 */
.pagination-section {
  display: flex;
  justify-content: right;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 4px;
  }

  .search-content {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input-wrapper,
  .search-select-wrapper {
    max-width: none;
  }

  .export-card {
    flex-direction: column;
    align-items: stretch;
  }

  .date-picker-wrapper {
    min-width: auto;
  }
}
</style>
