﻿<template>
  <div class="error-container">
    <div class="error-background">
      <div class="floating-elements">
        <div class="element element-1" />
        <div class="element element-2" />
        <div class="element element-3" />
        <div class="element element-4" />
      </div>
    </div>

    <div class="error-content">
      <div class="error-card">
        <div class="error-illustration">
          <div class="error-number">404</div>
          <div class="error-icons">
            <i class="el-icon-warning-outline" />
          </div>
        </div>

        <div class="error-info">
          <h1 class="error-title">页面未找到</h1>
          <p class="error-message">{{ message }}</p>
          <p class="error-description">
            抱歉，您访问的页面不存在或已被移动。请检查URL是否正确，或点击下方按钮返回首页。
          </p>

          <div class="error-actions">
            <el-button type="primary" size="large" @click="goHome">
              <i class="el-icon-house" />
              返回首页
            </el-button>

            <el-button size="large" @click="goBack">
              <i class="el-icon-back" />
              返回上页
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Page404",
  computed: {
    message() {
      return "您访问的页面走丢了...";
    },
  },
  methods: {
    goHome() {
      this.$router.push("/");
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.error-container {
  min-height: 100vh;
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.error-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  z-index: 0;

  .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .element {
      position: absolute;
      background: rgba(64, 158, 255, 0.1);
      border-radius: 50%;
      animation: float 8s ease-in-out infinite;

      &.element-1 {
        width: 120px;
        height: 120px;
        top: 15%;
        left: 10%;
        animation-delay: 0s;
      }

      &.element-2 {
        width: 80px;
        height: 80px;
        top: 25%;
        right: 20%;
        animation-delay: 2s;
      }

      &.element-3 {
        width: 60px;
        height: 60px;
        bottom: 30%;
        left: 15%;
        animation-delay: 4s;
      }

      &.element-4 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        right: 15%;
        animation-delay: 6s;
      }
    }
  }
}

.error-content {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 600px;
  padding: 6px;
}

.error-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 60px 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
  }
}

.error-illustration {
  margin-bottom: 12px;
  position: relative;

  .error-number {
    font-size: 8rem;
    font-weight: 900;
    background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    line-height: 1;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: linear-gradient(135deg, #409eff 0%, #1890ff 100%);
      border-radius: 2px;
    }
  }

  .error-icons {
    font-size: 3rem;
    color: #f59e0b;
    animation: bounce 2s ease-in-out infinite;
  }
}

.error-info {
  .error-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 16px 0;
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .error-message {
    font-size: 1.25rem;
    color: #409eff;
    margin: 0 0 20px 0;
    font-weight: 600;
  }

  .error-description {
    font-size: 1rem;
    color: #6b7280;
    line-height: 1.6;
    margin: 0 0 40px 0;
    max-width: 480px;
    margin-left: auto;
    margin-right: auto;
  }
}

.error-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-content {
    max-width: 90%;
    padding: 4px;
  }

  .error-card {
    padding: 40px 25px;
    border-radius: 8px;
  }

  .error-illustration {
    margin-bottom: 10px;

    .error-number {
      font-size: 6rem;
    }

    .error-icons {
      font-size: 2.5rem;
    }
  }

  .error-info {
    .error-title {
      font-size: 2rem;
    }

    .error-message {
      font-size: 1.1rem;
    }

    .error-description {
      font-size: 0.9rem;
      margin-bottom: 10px;
    }
  }

  .error-actions {
    flex-direction: column;
    align-items: center;

    .home-btn,
    .back-btn {
      width: 100%;
      max-width: 280px;
    }
  }
}

@media (max-width: 480px) {
  .error-card {
    padding: 30px 20px;
  }

  .error-illustration {
    .error-number {
      font-size: 4.5rem;
    }

    .error-icons {
      font-size: 2rem;
    }
  }

  .error-info {
    .error-title {
      font-size: 1.75rem;
    }

    .error-message {
      font-size: 1rem;
    }

    .error-description {
      font-size: 0.85rem;
    }
  }
}
</style>
