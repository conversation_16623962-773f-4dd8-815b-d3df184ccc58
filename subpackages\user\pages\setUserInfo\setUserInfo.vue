<template>
  <view class="set-user-info-container">
    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 用户信息卡片 -->
      <view class="user-card">
        <view class="card-title">基本信息</view>
        <view class="avatar-section">
          <view class="avatar-wrapper" @click="chooseImage">
            <image :src="userInfo.headPic === '' ? defaultHead : userInfo.headPic" class="user-avatar"></image>
            <view class="avatar-badge">
              <uni-icons type="camera" size="16" color="#fff" class="camera-icon"></uni-icons>
            </view>
          </view>
          <view class="user-info">
            <view class="phone-info">手机号</view>
            <view class="phone-info">{{ userInfo.phone || "未绑定手机" }}</view>
          </view>
        </view>

        <!-- 用户名显示（只读） -->
        <view class="info-display-group">
          <view class="display-label">用户名</view>
          <view class="info-display-wrapper">
            <view class="display-icon">
              <uni-icons type="person" size="16" color="#999" class="person-icon"></uni-icons>
            </view>
            <view class="info-display-text">
              {{ userInfo.userName || "未设置用户名" }}
            </view>
          </view>
        </view>
      </view>

      <!-- 账户信息卡片 -->
      <view class="info-card">
        <view class="card-title">账户信息</view>
        <view class="info-grid">
          <view class="info-item">
            <view class="info-icon">
              <uni-icons type="wallet" size="20" color="#00d4aa" class="wallet-icon"></uni-icons>
            </view>
            <view class="info-content">
              <view class="info-label">账户余额</view>
              <view class="info-value balance">¥{{ userInfo.integral || "0.00" }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view class="action-section">
        <button class="logout-btn" @click="logout">
          <text>退出登录</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { showInformations, setUserName, uploadImage } from "@/api/user.js";
import { resetInfo } from "@/utils/auth.js";
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      isSend: false,
      headPicUrl: "", // 新头像URL
      defaultHead:
        "https://elcxt.online:8199/files/javaForARResource/img/default_head.png",
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  methods: {
    logout() {
      uni.showModal({
        title: "退出登录",
        content: "确定要退出登录吗？",
        confirmText: "确定",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            resetInfo();
            this.$store.commit("user/DEFAULTUSERINFO");
            uni.reLaunch({
              url: "/pages/unifiedLogin/unifiedLogin",
            });
          }
        },
      });
    },
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          this.uploadImage(tempFilePath);
        },
      });
    },
    uploadImage(filePath) {
      if (this.isSend) {
        return;
      }
      this.isSend = true;
      uploadImage(filePath)
        .then((res) => {
          if (res.data.code === 200) {
            this.headPicUrl = res.data.data;
            // 直接更新用户头像
            const data = {
              username: this.userInfo.userName, // 保持用户名不变
              headPic: res.data.data,
            };
            return setUserName(data);
          } else {
            throw new Error("图片上传失败");
          }
        })
        .then((res) => {
          if (res && res.data.code === 200) {
            uni.showToast({
              title: "头像更新成功",
              icon: "success",
            });
            this.$store.dispatch("user/getUserInfo");
          } else {
            throw new Error("头像更新失败");
          }
        })
        .catch((err) => {
          console.error("上传失败:", err);
          uni.showToast({
            title: err.message || "头像更新失败",
            icon: "error",
          });
        })
        .finally(() => {
          this.isSend = false;
        });
    },
  },
  watch: {
    isSend(c) {
      if (c) {
        uni.showLoading({
          title: "加载中",
          mask: true,
        });
      } else {
        uni.hideLoading();
      }
    },
  },
  onLoad() {
    // 检查页面访问权限
    const { checkCurrentPageAccess } = require("@/utils/routeGuard.js");
    const { handlePagePermissionCheck } = require("@/utils/permissionChecker.js");

    // 使用路由守卫检查权限
    if (!checkCurrentPageAccess()) {
      return; // 如果无权限访问，路由守卫会自动重定向
    }

    // 严格的权限检查：基于adminRank的用户端页面权限验证
    if (!handlePagePermissionCheck("user", "用户端个人信息编辑页面")) {
      return; // 权限检查失败，会自动重定向
    }

    console.log("[UserSetUserInfo] 权限检查通过，允许访问用户端编辑页面");
  },
  onHide() {
    uni.hideLoading();
  },
};
</script>

<style lang="scss" scoped>
.set-user-info-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 主要内容 */
.main-content {
  padding: 20px;
}

/* 用户信息卡片 */
.user-card {
  background: #fff;
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.08);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  letter-spacing: 0.3px;
}

/* 头像区域 */
.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.avatar-wrapper {
  position: relative;
  margin-right: 20px;
}

.user-avatar {
  width: 70px;
  height: 70px;
  border-radius: 20px;
  border: 3px solid #00d4aa;
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.2);
}

.avatar-badge {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: #00d4aa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
}

.user-info {
  flex: 1;
}

.user-id {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 6px;
  letter-spacing: 0.3px;
}

.phone-info {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

/* 信息显示组 */
.info-display-group {
  margin-bottom: 20px;
}

.display-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.info-display-wrapper {
  position: relative;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  min-height: 44px;
  padding: 0 12px 0 40px;
}

.display-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.info-display-text {
  font-size: 15px;
  color: #333;
  font-weight: 500;
}

/* 信息卡片 */
.info-card {
  background: #fff;
  border-radius: 20px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.08);
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 15px;
  transition: all 0.3s ease;

  &:active {
    background: rgba(0, 212, 170, 0.05);
  }
}

.info-icon {
  width: 40px;
  height: 40px;
  background: rgba(0, 212, 170, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.info-content {
  flex: 1;
}

.info-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
  font-weight: 500;
}

.info-value {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  letter-spacing: 0.3px;

  &.balance {
    color: #00d4aa;
    font-size: 18px;
  }
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-top: 10px;
}

.action-section button {
  width: 100%;
  height: 50px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-sizing: border-box;
  letter-spacing: 1px;

  &:active {
    transform: scale(0.98);
  }

  text {
    color: inherit;
    font-size: 16px;
    font-weight: 500;
  }
}

.logout-btn {
  background: #fff;
  color: #ff6b6b;
  border: 2px solid #ff6b6b;
  display: flex;
  justify-content: center;
  align-items: center;

  &:active {
    background: #ff6b6b;
    color: #fff;
  }
}

/* Custom icon styles */
.custom-icon {
  font-size: 16px;
  display: inline-block;
}

.camera-icon {
  color: #fff;
  font-size: 12px;
}

.person-icon {
  color: #999 !important;
  font-size: 16px !important;
}

.wallet-icon {
  color: #00d4aa;
  font-size: 20px;
}
</style>
