import { Message } from "element-ui";

// 全局错误处理状态
let lastErrorMessage = null;
let lastErrorTime = 0;
const ERROR_DEBOUNCE_TIME = 3000; // 3秒内相同错误不重复显示

// 常见英文错误信息的中文映射
const ERROR_MESSAGE_MAP = {
  // 网络相关错误
  "Network Error": "网络连接异常，请检查网络设置",
  timeout: "请求超时，请稍后重试",
  TIMEOUT: "请求超时，请稍后重试",
  "Connection timeout": "连接超时，请稍后重试",
  "Request timeout": "请求超时，请稍后重试",

  // HTTP状态码错误
  "Request failed with status code 400": "请求参数错误",
  "Request failed with status code 401": "身份验证失败，请重新登录",
  "Request failed with status code 403": "没有权限访问此资源",
  "Request failed with status code 404": "请求的资源不存在",
  "Request failed with status code 405": "请求方法不被允许",
  "Request failed with status code 408": "请求超时，请稍后重试",
  "Request failed with status code 429": "请求过于频繁，请稍后重试",
  "Request failed with status code 500": "服务器内部错误，请稍后重试",
  "Request failed with status code 502": "网关错误，请稍后重试",
  "Request failed with status code 503": "服务暂时不可用，请稍后重试",
  "Request failed with status code 504": "网关超时，请稍后重试",

  // 系统级网络错误
  ECONNABORTED: "请求超时，请稍后重试",
  ENOTFOUND: "网络连接失败，请检查网络设置",
  ECONNREFUSED: "连接被拒绝，请稍后重试",
  EHOSTUNREACH: "网络不可达，请检查网络连接",
  ENETUNREACH: "网络不可达，请检查网络连接",
  ECONNRESET: "连接被重置，请稍后重试",

  // 现代浏览器错误码
  ERR_NETWORK: "网络连接异常，请检查网络设置",
  ERR_INTERNET_DISCONNECTED: "网络已断开，请检查网络连接",
  ERR_CONNECTION_REFUSED: "连接被拒绝，请稍后重试",
  ERR_CONNECTION_TIMED_OUT: "连接超时，请稍后重试",
  ERR_CONNECTION_RESET: "连接被重置，请稍后重试",
  ERR_NAME_NOT_RESOLVED: "域名解析失败，请检查网络设置",

  // 通用错误词汇
  Error: "操作失败",
  Failed: "操作失败",
  Unauthorized: "身份验证失败，请重新登录",
  Forbidden: "没有权限访问",
  "Not Found": "资源不存在",
  "Internal Server Error": "服务器内部错误",
  "Bad Gateway": "网关错误",
  "Service Unavailable": "服务暂时不可用",
  "Gateway Timeout": "网关超时",
};

/**
 * 将英文错误信息转换为中文
 * @param {string} message - 原始错误信息
 * @returns {string} 中文错误信息
 */
function translateErrorMessage(message) {
  if (!message || typeof message !== "string") {
    return message;
  }

  // 直接匹配
  if (ERROR_MESSAGE_MAP[message]) {
    return ERROR_MESSAGE_MAP[message];
  }

  // 模糊匹配
  for (const [key, value] of Object.entries(ERROR_MESSAGE_MAP)) {
    if (
      message.includes(key) ||
      message.toLowerCase().includes(key.toLowerCase())
    ) {
      return value;
    }
  }

  // 处理状态码错误
  const statusCodeMatch = message.match(
    /Request failed with status code (\d+)/
  );
  if (statusCodeMatch) {
    const statusCode = statusCodeMatch[1];
    const statusMessages = {
      400: "请求参数错误",
      401: "身份验证失败，请重新登录",
      403: "没有权限访问此资源",
      404: "请求的资源不存在",
      405: "请求方法不被允许",
      408: "请求超时，请稍后重试",
      429: "请求过于频繁，请稍后重试",
      500: "服务器内部错误，请稍后重试",
      502: "网关错误，请稍后重试",
      503: "服务暂时不可用，请稍后重试",
      504: "网关超时，请稍后重试",
    };
    return (
      statusMessages[statusCode] || `服务器错误 (${statusCode})，请稍后重试`
    );
  }

  return message;
}

/**
 * 统一的错误消息处理函数
 * @param {*} error - 错误对象
 * @param {string} defaultMessage - 默认错误消息
 * @param {boolean} showMessage - 是否显示消息，默认为true
 * @returns {string} 最终的错误消息
 */
export function handleError(
  error,
  defaultMessage = "操作失败，请稍后重试",
  showMessage = true
) {
  let finalMessage = defaultMessage;
  const currentTime = Date.now();

  // 优先使用接口返回的错误信息
  if (error && typeof error === "object") {
    // 处理axios错误响应
    if (error.response && error.response.data) {
      const responseData = error.response.data;
      if (responseData.msg) {
        finalMessage = responseData.msg;
      } else if (responseData.message) {
        finalMessage = responseData.message;
      } else if (responseData.data && responseData.data.msg) {
        finalMessage = responseData.data.msg;
      } else if (responseData.data && responseData.data.message) {
        finalMessage = responseData.data.message;
      }
    }
    // 处理直接的错误对象
    else if (error.msg) {
      finalMessage = error.msg;
    } else if (error.message) {
      finalMessage = error.message;
    } else if (error.data && error.data.msg) {
      finalMessage = error.data.msg;
    } else if (error.data && error.data.message) {
      finalMessage = error.data.message;
    }
  } else if (typeof error === "string") {
    finalMessage = error;
  }

  // 将错误信息转换为中文
  finalMessage = translateErrorMessage(finalMessage);

  // 防抖处理，避免短时间内重复显示相同错误
  if (showMessage) {
    if (
      finalMessage !== lastErrorMessage ||
      currentTime - lastErrorTime > ERROR_DEBOUNCE_TIME
    ) {
      Message({
        message: finalMessage,
        type: "error",
        duration: 5000,
      });
      lastErrorMessage = finalMessage;
      lastErrorTime = currentTime;
    }
  }

  return finalMessage;
}

/**
 * 清除错误状态（用于手动重置防抖）
 */
export function clearErrorState() {
  lastErrorMessage = null;
  lastErrorTime = 0;
}

/**
 * 处理网络请求成功但业务失败的情况
 * @param {*} response - 响应对象
 * @param {string} defaultMessage - 默认错误消息
 * @returns {boolean} 是否为业务成功
 */
export function handleBusinessError(response, defaultMessage = "操作失败") {
  if (!response || !response.data) {
    handleError(null, defaultMessage);
    return false;
  }

  const data = response.data;

  // 判断业务是否成功（根据实际后端返回的格式调整）
  if (data.code !== 200 && data.code !== "200") {
    const errorMsg =
      data.msg ||
      data.message ||
      data.data?.msg ||
      data.data?.message ||
      defaultMessage;
    handleError(errorMsg, defaultMessage);
    return false;
  }

  return true;
}
