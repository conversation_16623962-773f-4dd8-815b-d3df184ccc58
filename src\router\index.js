import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: "/login",
    component: () => import("@/views/login/index"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/404"),
    hidden: true,
  },
  {
    path: "/",
    component: Layout,
    redirect: "/dataShow/main",
    hidden: true,
  },
  {
    path: "/dashboard",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "index",
        name: "Dashboard",
        component: () => import("@/views/dashboard/index"),
        meta: {
          title: "Dashboard",
          icon: "dashboard",
        },
      },
    ],
  },
  {
    path: "/dataShow",
    component: Layout,
    meta: {
      title: "数据可视化",
      icon: "el-icon-data-line",
      breadcrumb: false, // 隐藏父级路由在面包屑中的显示
    },
    children: [
      {
        path: "main",
        name: "dataShow",
        component: () => import("@/views/dataShow"),
        meta: {
          title: "数据可视化",
          icon: "el-icon-data-line",
        },
      },
    ],
  },
  {
    path: "/userList",
    component: Layout,
    meta: {
      title: "用户列表",
      icon: "el-icon-user",
      breadcrumb: false, // 隐藏父级路由在面包屑中的显示
    },
    children: [
      {
        path: "main",
        name: "userList",
        component: () => import("@/views/userList"),
        meta: {
          title: "用户列表",
          icon: "el-icon-user",
        },
      },
    ],
  },
  {
    path: "/ProxyList",
    component: Layout,
    meta: {
      title: "代理",
      icon: "el-icon-s-custom",
    },
    children: [
      {
        path: "main",
        name: "proxyList",
        component: () => import("@/views/proxyList"),
        meta: {
          title: "代理列表",
          icon: "el-icon-s-custom",
        },
      },
      {
        path: "performance",
        name: "performance",
        component: () => import("@/views/performance"),
        meta: {
          title: "业绩管理",
          icon: "el-icon-s-custom",
        },
        hidden: true,
      },
    ],
  },
  {
    path: "/flGoods",
    component: Layout,
    meta: {
      title: "福禄权益",
      icon: "el-icon-s-goods",
      breadcrumb: false, // 隐藏父级路由在面包屑中的显示
    },
    children: [
      {
        path: "main",
        name: "flGoods",
        component: () => import("@/views/flGoods"),
        meta: {
          title: "福禄权益",
          icon: "el-icon-s-goods",
        },
      },
    ],
  },

  {
    path: "/productConfig",
    component: Layout,
    meta: {
      title: "产品配置",
      icon: "el-icon-setting",
    },
    children: [
      {
        path: "creditGoods",
        name: "creditGoods",
        component: () => import("@/views/creditGoods"),
        meta: {
          title: "话费配置",
          icon: "el-icon-phone",
        },
      },
      {
        path: "equityGoods",
        name: "equityGoods",
        component: () => import("@/views/equityGoods"),
        meta: {
          title: "权益配置",
          icon: "el-icon-present",
        },
      },
    ],
  },

  {
    path: "/order",
    component: Layout,
    meta: {
      title: "订单模块",
      icon: "el-icon-goods",
    },
    children: [
      {
        path: "main",
        name: "order",
        component: () => import("@/views/order"),
        meta: {
          title: "订单列表",
          icon: "el-icon-goods",
        },
      },
      {
        path: "refund",
        name: "refund",
        component: () => import("@/views/order/refund"),
        meta: {
          title: "话费充值订单",
          icon: "el-icon-sold-out",
        },
      },
      {
        path: "withdraw",
        name: "withdraw",
        component: () => import("@/views/order/withdraw"),
        meta: {
          title: "银盛提现订单",
          icon: "el-icon-sold-out",
        },
      },
    ],
  },
  {
    path: "/selfOperated",
    component: Layout,
    meta: {
      title: "自营模块",
      icon: "el-icon-goods",
    },
    children: [
      {
        path: "selfGoods",
        name: "selfGoods",
        component: () => import("@/views/selfOperated/selfGoods"),
        meta: {
          title: "自营商品列表",
          icon: "el-icon-goods",
        },
      },
      {
        path: "selfOrder",
        name: "selfOrder",
        component: () => import("@/views/selfOperated/selfOrder"),
        meta: {
          title: "自营订单列表",
          icon: "el-icon-sold-out",
        },
      },
    ],
  },

  {
    path: "/profile",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "index",
        name: "Profile",
        component: () => import("@/views/profile"),
        meta: {
          title: "个人信息",
          icon: "el-icon-user",
        },
      },
    ],
  },
  {
    path: "/comments",
    component: Layout,
    meta: {
      title: "评论管理",
      icon: "el-icon-user",
      breadcrumb: false, // 隐藏父级路由在面包屑中的显示
    },
    children: [
      {
        path: "main",
        name: "wechatComments",
        component: () => import("@/views/comments/wechatComments"),
        meta: {
          title: "评论管理",
          icon: "el-icon-user",
        },
      },
    ],
  },
  {
    path: "/chat",
    component: Layout,
    meta: {
      title: "客服模块",
      icon: "el-icon-phone-outline",
      breadcrumb: false, // 隐藏父级路由在面包屑中的显示
    },
    children: [
      {
        path: "main",
        name: "chat",
        component: () => import("@/views/chat"),
        meta: {
          title: "客服模块",
          icon: "el-icon-phone-outline",
        },
      },
    ],
  },
  {
    path: "/uploadVersion",
    component: Layout,
    meta: {
      title: "上传",
      icon: "el-icon-upload2",
    },
    children: [
      {
        path: "main",
        name: "uploadVersion",
        component: () => import("@/views/uploadVersion"),
        meta: {
          title: "上传App",
          icon: "el-icon-upload2",
        },
      },
    ],
  },
  {
    path: "/systemManage",
    component: Layout,
    meta: {
      title: "系统管理",
      icon: "el-icon-setting",
      breadcrumb: false, // 隐藏父级路由在面包屑中的显示
    },
    children: [
      {
        path: "main",
        name: "systemManage",
        component: () => import("@/views/systemManage"),
        meta: {
          title: "系统管理",
          icon: "el-icon-setting",
        },
      },
    ],
  },

  // 用户端话费充值相关路由 (隐藏路由，用于小程序webview访问)
  {
    path: "/userCredit",
    component: () => import("@/views/userCredit/index"),
    hidden: true,
    meta: {
      title: "话费充值",
    },
  },
  {
    path: "/userCredit/warning",
    component: () => import("@/views/userCredit/warning"),
    hidden: true,
    meta: {
      title: "充值温馨提示",
    },
  },


  // 404 page must be placed at the end !!!
  {
    path: "*",
    redirect: "/404",
    hidden: true,
  },
];

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({
      y: 0,
    }),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
